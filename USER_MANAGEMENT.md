# User Management Guide

## Overview

This guide shows you how to create and manage admin users for your Letz.ai Admin Dashboard with enhanced security features.

## Quick Start

### Method 1: Interactive Script (Recommended)

Create an admin user with the interactive script:

```bash
npm run create:admin
```

This will:
- Ask for user details (name, email, role)
- Validate password strength
- Generate secure password hash
- Create SQL script to insert the user

### Method 2: Command Line Interface

Use the CLI for batch operations:

```bash
# Create a new admin user
npm run admin:cli create-user \
  --name "<PERSON>" \
  --email "<EMAIL>" \
  --password "SecurePassword123!" \
  --role "admin"

# Generate a secure password
npm run admin:cli generate-password --length 16

# Hash a password for manual insertion
npm run admin:cli hash-password --password "MySecurePassword123!"
```

### Method 3: Simple User (Current Table)

Create a basic user in the existing user_account table:

```bash
npm run create:user
```

## User Roles

### Available Roles

1. **super_admin**
   - Full system access
   - Can manage all users and settings
   - Can access all resources

2. **admin**
   - Most administrative functions
   - Can manage users, models, images
   - Can view statistics and audit logs

3. **moderator**
   - Content moderation
   - User management (limited)
   - Can edit content and organizations

4. **viewer**
   - Read-only access
   - Can view all data but cannot modify

### Role Permissions

| Permission | Super Admin | Admin | Moderator | Viewer |
|------------|-------------|-------|-----------|--------|
| User Management | ✅ | ✅ | ✅ | ❌ |
| Model Management | ✅ | ✅ | ✅ | ❌ |
| Image Management | ✅ | ✅ | ✅ | ❌ |
| Statistics | ✅ | ✅ | ✅ | ✅ |
| Audit Logs | ✅ | ✅ | ❌ | ❌ |
| System Settings | ✅ | ❌ | ❌ | ❌ |
| User Creation | ✅ | ✅ | ❌ | ❌ |

## Database Setup

### 1. Create Security Tables

First, run the database migration to create the admin user tables:

```sql
-- Run this SQL in your database
-- File: src/migrations/001-create-admin-security-tables.sql

CREATE TABLE IF NOT EXISTS admin_user (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'viewer',
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    two_factor_secret VARCHAR(255),
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    allowed_ip_addresses JSONB,
    last_login_at TIMESTAMP,
    last_login_ip VARCHAR(45),
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    password_changed_at TIMESTAMP,
    must_change_password BOOLEAN DEFAULT TRUE,
    permissions JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_admin_email ON admin_user(email);
```

### 2. Insert Your First Admin User

After creating a user with the scripts above, run the generated SQL:

```sql
-- Example (generated by the script)
INSERT INTO admin_user (
    id,
    name, 
    email, 
    password_hash, 
    role, 
    status, 
    must_change_password,
    password_changed_at,
    created_at,
    updated_at
) VALUES (
    'uuid-here',
    'Your Name',
    '<EMAIL>',
    '$2b$12$hashedpasswordhere',
    'super_admin',
    'active',
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);
```

## Password Requirements

All passwords must meet these security requirements:

- **Minimum 12 characters** long
- Contains **uppercase** letters (A-Z)
- Contains **lowercase** letters (a-z)
- Contains **numbers** (0-9)
- Contains **special characters** (!@#$%^&*()_+-=[]{}|;:,.<>?)

### Example Strong Passwords

```
SecureAdmin2024!
MyP@ssw0rd123
Adm1n!strat0r
```

## CLI Commands Reference

### Create User
```bash
npm run admin:cli create-user \
  --name "Full Name" \
  --email "<EMAIL>" \
  --password "SecurePassword123!" \
  --role "admin" \
  --output "user.sql"
```

### Generate Password
```bash
npm run admin:cli generate-password --length 16
```

### Hash Password
```bash
npm run admin:cli hash-password --password "YourPassword123!"
```

## Security Features

### Account Lockout
- **5 failed login attempts** triggers account lockout
- **15-minute lockout duration** (configurable)
- Automatic unlock after lockout period

### Session Management
- **8-hour session duration** (configurable)
- **Secure session tokens**
- **IP address validation** (optional)

### Two-Factor Authentication
- **TOTP support** (Google Authenticator, Authy)
- **QR code generation** for easy setup
- **Backup codes** (planned feature)

## Troubleshooting

### Common Issues

#### 1. "Email already exists"
```sql
-- Update existing user instead
UPDATE admin_user 
SET password_hash = 'new-hash', updated_at = CURRENT_TIMESTAMP 
WHERE email = '<EMAIL>';
```

#### 2. "Password validation failed"
Make sure your password meets all requirements:
- At least 12 characters
- Mixed case letters
- Numbers and special characters

#### 3. "Cannot connect to database"
- Check your database connection settings in `.env`
- Ensure the database is running
- Verify credentials

### Reset User Password

```sql
-- Reset password and force change on next login
UPDATE admin_user 
SET 
    password_hash = 'new-bcrypt-hash',
    must_change_password = TRUE,
    failed_login_attempts = 0,
    locked_until = NULL,
    updated_at = CURRENT_TIMESTAMP
WHERE email = '<EMAIL>';
```

### Unlock User Account

```sql
-- Unlock a locked user account
UPDATE admin_user 
SET 
    failed_login_attempts = 0,
    locked_until = NULL,
    status = 'active',
    updated_at = CURRENT_TIMESTAMP
WHERE email = '<EMAIL>';
```

## API Endpoints (When Enabled)

Once the full security system is active, you can use these API endpoints:

```bash
# Get all users
GET /api/admin-users

# Create user
POST /api/admin-users
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "role": "admin"
}

# Update user
PUT /api/admin-users/:id
{
  "name": "Updated Name",
  "role": "moderator"
}

# Change password
POST /api/admin-users/:id/change-password
{
  "currentPassword": "old-password",
  "newPassword": "new-password"
}

# Lock/unlock user
POST /api/admin-users/:id/lock
POST /api/admin-users/:id/unlock
```

## Next Steps

1. **Create your first admin user** using the interactive script
2. **Run the database migration** to create security tables
3. **Test login** with the new credentials
4. **Enable Node.js 18** for full AdminJS functionality
5. **Set up two-factor authentication** for enhanced security

## Security Best Practices

1. **Use unique, strong passwords** for each admin account
2. **Enable 2FA** for all admin users
3. **Regular password changes** (every 90 days)
4. **Monitor audit logs** for suspicious activity
5. **Use IP restrictions** in production environments
6. **Regular security reviews** of user accounts and permissions
