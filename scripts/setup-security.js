#!/usr/bin/env node

/**
 * Security Setup Script for Letz.ai Admin Dashboard
 * 
 * This script helps set up the enhanced security features:
 * 1. Generates secure secrets for sessions and cookies
 * 2. Creates the first admin user
 * 3. Sets up database tables
 * 4. Provides security recommendations
 */

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import readline from 'readline';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const envPath = path.join(__dirname, '..', '.env');
const envDistPath = path.join(__dirname, '..', '.env.dist');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(query) {
    return new Promise(resolve => rl.question(query, resolve));
}

function generateSecureSecret() {
    return crypto.randomBytes(32).toString('hex');
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePassword(password) {
    const minLength = 12;
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
    
    const errors = [];
    if (password.length < minLength) errors.push(`Password must be at least ${minLength} characters long`);
    if (!hasUpper) errors.push('Password must contain at least one uppercase letter');
    if (!hasLower) errors.push('Password must contain at least one lowercase letter');
    if (!hasNumber) errors.push('Password must contain at least one number');
    if (!hasSpecial) errors.push('Password must contain at least one special character');
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

async function setupEnvironment() {
    console.log('🔐 Letz.ai Admin Dashboard Security Setup\n');
    
    // Check if .env exists
    let envContent = '';
    if (fs.existsSync(envPath)) {
        console.log('📄 Found existing .env file. We\'ll update it with security settings.\n');
        envContent = fs.readFileSync(envPath, 'utf8');
    } else if (fs.existsSync(envDistPath)) {
        console.log('📄 Creating .env file from .env.dist template.\n');
        envContent = fs.readFileSync(envDistPath, 'utf8');
    } else {
        console.error('❌ No .env.dist template found. Please ensure the file exists.');
        process.exit(1);
    }
    
    // Generate secure secrets
    const sessionSecret = generateSecureSecret();
    const cookieSecret = generateSecureSecret();
    
    console.log('🔑 Generated secure secrets for sessions and cookies.\n');
    
    // Update environment variables
    envContent = envContent.replace(/SESSION_SECRET=.*/, `SESSION_SECRET=${sessionSecret}`);
    envContent = envContent.replace(/COOKIE_SECRET=.*/, `COOKIE_SECRET=${cookieSecret}`);
    
    // Get admin user details
    console.log('👤 Let\'s create your first admin user:\n');
    
    let adminName, adminEmail, adminPassword;
    
    // Get admin name
    adminName = await question('Enter admin name: ');
    while (!adminName.trim()) {
        adminName = await question('Admin name cannot be empty. Enter admin name: ');
    }
    
    // Get admin email
    adminEmail = await question('Enter admin email: ');
    while (!validateEmail(adminEmail)) {
        adminEmail = await question('Invalid email format. Enter admin email: ');
    }
    
    // Get admin password
    console.log('\\nPassword requirements:');
    console.log('- At least 12 characters long');
    console.log('- Contains uppercase and lowercase letters');
    console.log('- Contains at least one number');
    console.log('- Contains at least one special character\\n');
    
    adminPassword = await question('Enter admin password: ');
    let passwordValidation = validatePassword(adminPassword);
    
    while (!passwordValidation.isValid) {
        console.log('\\n❌ Password validation failed:');
        passwordValidation.errors.forEach(error => console.log(`   - ${error}`));
        console.log('');
        adminPassword = await question('Enter admin password: ');
        passwordValidation = validatePassword(adminPassword);
    }
    
    console.log('\\n✅ Password meets security requirements.\\n');
    
    // Security options
    const enforceIp = await question('Enforce IP address validation for sessions? (y/N): ');
    const allowedIps = await question('Enter allowed IP addresses (comma-separated, or press Enter to allow all): ');
    
    // Update environment with security settings
    envContent = envContent.replace(/ENFORCE_SESSION_IP=.*/, `ENFORCE_SESSION_IP=${enforceIp.toLowerCase() === 'y'}`);
    if (allowedIps.trim()) {
        envContent = envContent.replace(/ALLOWED_IPS=.*/, `ALLOWED_IPS=${allowedIps.trim()}`);
    }
    
    // Write .env file
    fs.writeFileSync(envPath, envContent);
    console.log('✅ Environment configuration updated.\\n');
    
    // Create admin user setup script
    const setupScript = `
-- Enhanced Security Setup for Letz.ai Admin Dashboard
-- Run this script to create the admin user and security tables

-- Create admin user with provided credentials
INSERT INTO admin_user (
    name, 
    email, 
    password_hash, 
    role, 
    status, 
    must_change_password,
    password_changed_at
) VALUES (
    '${adminName.replace(/'/g, "''")}',
    '${adminEmail.toLowerCase()}',
    -- You'll need to hash this password: ${adminPassword}
    '$2b$12$PLACEHOLDER_HASH', -- Replace with actual bcrypt hash
    'super_admin',
    'active',
    FALSE,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO UPDATE SET
    name = EXCLUDED.name,
    password_hash = EXCLUDED.password_hash,
    updated_at = CURRENT_TIMESTAMP;
`;
    
    fs.writeFileSync(path.join(__dirname, '..', 'setup-admin-user.sql'), setupScript);
    
    console.log('📋 Security Setup Complete!\\n');
    console.log('Next steps:');
    console.log('1. Run the database migration: npm run migration:run');
    console.log('2. Hash the admin password and update the SQL script');
    console.log('3. Run the admin user setup script');
    console.log('4. Start the application: npm run start:dev');
    console.log('5. Login with your new credentials\\n');
    
    console.log('🔒 Security Recommendations:');
    console.log('- Change default API keys in production');
    console.log('- Use HTTPS in production');
    console.log('- Regularly review audit logs');
    console.log('- Enable two-factor authentication');
    console.log('- Keep dependencies updated');
    console.log('- Monitor for suspicious activity\\n');
    
    rl.close();
}

// Run the setup
setupEnvironment().catch(error => {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
});
