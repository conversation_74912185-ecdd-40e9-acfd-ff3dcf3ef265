#!/bin/sh
set -e

echo "🚀 Starting LetzAI Admin container..."

# Function to install Rollup native binaries
install_rollup_native() {
    echo "📦 Installing Rollup native binaries for ARM64..."

    # Detect architecture
    ARCH=$(node -e "console.log(process.arch)")
    PLATFORM=$(node -e "console.log(process.platform)")

    echo "Detected platform: $PLATFORM, architecture: $ARCH"

    # Install the correct Rollup native package
    if [ "$PLATFORM" = "linux" ] && [ "$ARCH" = "arm64" ]; then
        echo "Installing ARM64 musl Rollup binary..."
        npm install @rollup/rollup-linux-arm64-musl --save-optional --no-audit --no-fund --silent || true
        npm install @rollup/rollup-linux-arm64-gnu --save-optional --no-audit --no-fund --silent || true
    elif [ "$PLATFORM" = "linux" ] && [ "$ARCH" = "x64" ]; then
        echo "Installing x64 musl Rollup binary..."
        npm install @rollup/rollup-linux-x64-musl --save-optional --no-audit --no-fund --silent || true
        npm install @rollup/rollup-linux-x64-gnu --save-optional --no-audit --no-fund --silent || true
    fi

    # Verify installation
    if [ -d "node_modules/@rollup" ]; then
        echo "✅ Rollup native binaries installed:"
        ls -la node_modules/@rollup/ || true
    else
        echo "⚠️  No Rollup native binaries found, but continuing..."
    fi
}

# Function to fix permissions
fix_permissions() {
    echo "🔧 Fixing permissions..."
    chmod +x /usr/src/app/node_modules/.bin/* 2>/dev/null || true
}

# Function to check if we need to install dependencies
check_dependencies() {
    if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then
        echo "📦 Installing dependencies..."
        npm install
    fi
}

# Main execution
cd /usr/src/app

# Check and install dependencies if needed
check_dependencies

# Install Rollup native binaries
install_rollup_native

# Fix permissions
fix_permissions

# Execute the command passed to the script
echo "🎯 Executing command: $@"
exec "$@"
