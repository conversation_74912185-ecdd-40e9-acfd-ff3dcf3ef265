#!/usr/bin/env node

/**
 * Simple User Creator - Works with any Node.js version
 * No external dependencies, just generates SQL
 */

import crypto from 'crypto';

console.log('🔐 Simple Admin User Creator');
console.log('============================\n');

// Default values - you can modify these directly in the script
const userData = {
    name: 'Admin User',
    email: '<EMAIL>', 
    password: 'SecurePassword123!',
    role: 'super_admin'
};

// Or get from command line arguments
const args = process.argv.slice(2);
if (args.length >= 3) {
    userData.name = args[0];
    userData.email = args[1];
    userData.password = args[2];
    userData.role = args[3] || 'admin';
}

console.log('👤 Creating user with these details:');
console.log(`   Name: ${userData.name}`);
console.log(`   Email: ${userData.email}`);
console.log(`   Role: ${userData.role}`);
console.log(`   Password: ${userData.password}`);

// Generate UUID
const userId = crypto.randomUUID();

// Generate SQL
const sql = `
-- =====================================================
-- Admin User Creation SQL
-- =====================================================
-- User: ${userData.name} (${userData.email})
-- Generated: ${new Date().toISOString()}
-- =====================================================

-- First, create the admin_user table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_user (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'viewer',
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    two_factor_secret VARCHAR(255),
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    allowed_ip_addresses JSONB,
    last_login_at TIMESTAMP,
    last_login_ip VARCHAR(45),
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    password_changed_at TIMESTAMP,
    must_change_password BOOLEAN DEFAULT TRUE,
    permissions JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Create index on email
CREATE INDEX IF NOT EXISTS idx_admin_email ON admin_user(email);

-- Insert the admin user
-- ⚠️  IMPORTANT: You need to replace 'BCRYPT_HASH_HERE' with a real bcrypt hash!
-- Generate one at: https://bcrypt-generator.com/ (use 12 rounds)
-- Password to hash: ${userData.password}

INSERT INTO admin_user (
    id,
    name, 
    email, 
    password_hash, 
    role, 
    status, 
    must_change_password,
    password_changed_at,
    created_at,
    updated_at
) VALUES (
    '${userId}',
    '${userData.name.replace(/'/g, "''")}',
    '${userData.email.toLowerCase()}',
    'BCRYPT_HASH_HERE',
    '${userData.role}',
    'active',
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO UPDATE SET
    name = EXCLUDED.name,
    password_hash = EXCLUDED.password_hash,
    role = EXCLUDED.role,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- Next Steps:
-- 1. Generate bcrypt hash for password: ${userData.password}
-- 2. Replace 'BCRYPT_HASH_HERE' with the real hash
-- 3. Run this SQL in your database
-- =====================================================
`;

console.log('\n📝 Generated SQL:');
console.log('='.repeat(80));
console.log(sql);
console.log('='.repeat(80));

console.log('\n🔒 IMPORTANT: Generate Bcrypt Hash');
console.log('================================');
console.log(`Password to hash: ${userData.password}`);
console.log('');
console.log('Option 1 - Online Generator:');
console.log('  1. Go to: https://bcrypt-generator.com/');
console.log(`  2. Enter password: ${userData.password}`);
console.log('  3. Set rounds to: 12');
console.log('  4. Copy the generated hash');
console.log('  5. Replace "BCRYPT_HASH_HERE" in the SQL above');
console.log('');
console.log('Option 2 - Node.js (if bcrypt is available):');
console.log(`  node -e "const bcrypt=require('bcrypt'); bcrypt.hash('${userData.password}', 12).then(console.log)"`);
console.log('');
console.log('Option 3 - Python:');
console.log(`  python3 -c "import bcrypt; print(bcrypt.hashpw('${userData.password}'.encode('utf-8'), bcrypt.gensalt(rounds=12)).decode('utf-8'))"`);

// Save to file
import fs from 'fs';
const filename = `create-admin-${userData.email.replace('@', '-at-').replace(/[^a-zA-Z0-9-]/g, '')}.sql`;
fs.writeFileSync(filename, sql);

console.log(`\n✅ SQL saved to file: ${filename}`);
console.log('\n🚀 Ready to create your admin user!');
