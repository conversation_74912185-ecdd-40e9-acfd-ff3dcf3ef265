#!/bin/sh
set -e

echo "🚀 Starting LetzAI Admin (Production Mode)..."

# Quick environment check
if [ "$NODE_ENV" != "production" ]; then
    echo "⚠️  Warning: NODE_ENV is not set to production"
fi

# Verify required files exist
if [ ! -f "dist/main.js" ]; then
    echo "❌ Error: dist/main.js not found. Build may have failed."
    exit 1
fi

# Log startup info
echo "📊 Container Memory: ${CONTAINER_MEMORY_MB:-1024}MB"
echo "🔧 Node Options: ${NODE_OPTIONS}"
echo "🌐 Port: ${PORT:-3013}"

# Execute the command with tini if available, otherwise directly
echo "🎯 Starting application..."
if command -v tini >/dev/null 2>&1; then
    exec tini -- "$@"
else
    exec "$@"
fi
