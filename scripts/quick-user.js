#!/usr/bin/env node

/**
 * Quick User Creation - No Dependencies
 * Simple script to generate SQL for creating admin users
 */

import crypto from 'crypto';

// Get command line arguments
const args = process.argv.slice(2);

if (args.length < 3) {
    console.log('Usage: node quick-user.js <name> <email> <password> [role]');
    console.log('Example: node quick-user.js "Admin User" "<EMAIL>" "SecurePassword123!" "admin"');
    process.exit(1);
}

const [name, email, password, role = 'admin'] = args;

// Basic password validation
function validatePassword(password) {
    const minLength = 12;
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
    
    const errors = [];
    if (password.length < minLength) errors.push(`Password must be at least ${minLength} characters long`);
    if (!hasUpper) errors.push('Password must contain at least one uppercase letter');
    if (!hasLower) errors.push('Password must contain at least one lowercase letter');
    if (!hasNumber) errors.push('Password must contain at least one number');
    if (!hasSpecial) errors.push('Password must contain at least one special character');
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

// Validate email
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Validate inputs
if (!validateEmail(email)) {
    console.error('❌ Invalid email format');
    process.exit(1);
}

const passwordValidation = validatePassword(password);
if (!passwordValidation.isValid) {
    console.error('❌ Password validation failed:');
    passwordValidation.errors.forEach(error => console.error(`   - ${error}`));
    process.exit(1);
}

const validRoles = ['super_admin', 'admin', 'moderator', 'viewer'];
if (!validRoles.includes(role)) {
    console.error(`❌ Invalid role. Must be one of: ${validRoles.join(', ')}`);
    process.exit(1);
}

// Generate user ID
const userId = crypto.randomUUID();

// Note: This generates a placeholder hash - you'll need to replace it with a real bcrypt hash
const placeholderHash = '$2b$12$PLACEHOLDER_REPLACE_WITH_REAL_BCRYPT_HASH';

const sql = `
-- Create admin user: ${name} (${email})
-- ⚠️  IMPORTANT: Replace the password hash below with a real bcrypt hash!
-- You can generate one at: https://bcrypt-generator.com/ (use 12 rounds)
-- Or use: npm run admin:cli hash-password --password "${password}"

INSERT INTO admin_user (
    id,
    name, 
    email, 
    password_hash, 
    role, 
    status, 
    must_change_password,
    password_changed_at,
    created_at,
    updated_at
) VALUES (
    '${userId}',
    '${name.replace(/'/g, "''")}',
    '${email.toLowerCase()}',
    '${placeholderHash}',
    '${role}',
    'active',
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO UPDATE SET
    name = EXCLUDED.name,
    password_hash = EXCLUDED.password_hash,
    role = EXCLUDED.role,
    updated_at = CURRENT_TIMESTAMP;
`;

console.log('🔐 Admin User SQL Generator');
console.log('===========================\n');

console.log('👤 User Details:');
console.log(`   Name: ${name}`);
console.log(`   Email: ${email}`);
console.log(`   Role: ${role}`);
console.log(`   Password: ${password}`);

console.log('\n📝 Generated SQL:');
console.log('='.repeat(60));
console.log(sql);
console.log('='.repeat(60));

console.log('\n🔒 Next Steps:');
console.log('1. Generate bcrypt hash for the password');
console.log('2. Replace PLACEHOLDER_REPLACE_WITH_REAL_BCRYPT_HASH with the real hash');
console.log('3. Run the SQL in your database');

console.log('\n💡 Quick bcrypt hash generation:');
console.log(`   Online: https://bcrypt-generator.com/ (use 12 rounds)`);
console.log(`   CLI: npm run admin:cli hash-password --password "${password}"`);
