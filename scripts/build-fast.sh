#!/bin/bash
set -e

echo "⚡ Ultra-Fast Docker Build for LetzAI Admin"
echo "=========================================="

# Default values
IMAGE_NAME="letzai-admin"
TAG="fast"
SIMPLE_MODE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        --tag)
            TAG="$2"
            shift 2
            ;;
        --simple)
            SIMPLE_MODE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --name NAME    Image name (default: letzai-admin)"
            echo "  --tag TAG      Image tag (default: fast)"
            echo "  --simple       Build simple mode (no AdminJS)"
            echo "  --help         Show this help"
            echo ""
            echo "This script builds locally first, then creates a fast container."
            echo "Expected startup time: 1-3 seconds"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

FULL_IMAGE_NAME="$IMAGE_NAME:$TAG"

echo "📋 Build Configuration:"
echo "   Image: $FULL_IMAGE_NAME"
echo "   Simple Mode: $SIMPLE_MODE"
echo ""

# Step 1: Check if build exists or build locally
echo "🔨 Step 1: Checking application build..."
if [ "$SIMPLE_MODE" = true ]; then
    if [ ! -f "dist/simple-main.js" ]; then
        echo "Building in simple mode..."
        npm run build
        if [ ! -f "dist/simple-main.js" ]; then
            echo "❌ Error: simple-main.js not found after build"
            exit 1
        fi
    else
        echo "✅ Using existing simple-main.js build"
    fi
else
    if [ ! -f "dist/main.js" ]; then
        echo "Building in full mode..."
        npm run build
        if [ ! -f "dist/main.js" ]; then
            echo "❌ Error: main.js not found after build"
            exit 1
        fi
    else
        echo "✅ Using existing main.js build"
    fi
fi

echo "✅ Build ready!"

# Step 2: Create optimized .dockerignore for fast build
echo "🔧 Step 2: Creating optimized .dockerignore..."
cat > .dockerignore.fast << EOF
# Exclude everything except what we need
*
!package.json
!dist/
!scripts/docker-entrypoint-fast.sh
EOF

# Step 3: Build Docker image
echo "🐳 Step 3: Building Docker image..."
docker build \
    --file Dockerfile.fast \
    --tag "$FULL_IMAGE_NAME" \
    --progress=plain \
    .

# Cleanup
rm -f .dockerignore.fast

if [ $? -eq 0 ]; then
    echo "✅ Ultra-fast build completed!"
    
    # Show image size
    echo "📊 Image size:"
    docker images "$FULL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
    
    echo ""
    echo "🚀 Ready to run:"
    if [ "$SIMPLE_MODE" = true ]; then
        echo "   docker run -p 3013:3013 $FULL_IMAGE_NAME node dist/simple-main.js"
        echo ""
        echo "⚡ Simple mode: 1-2 second startup, no AdminJS"
    else
        echo "   docker run -p 3013:3013 $FULL_IMAGE_NAME"
        echo ""
        echo "⚡ Full mode: 2-3 second startup with AdminJS"
    fi
    echo ""
    echo "🎯 Expected startup time: 1-3 seconds maximum!"
else
    echo "❌ Build failed!"
    exit 1
fi
