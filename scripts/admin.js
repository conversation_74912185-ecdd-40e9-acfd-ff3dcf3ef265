#!/usr/bin/env node

/**
 * Letz.ai Admin CLI Tool
 * Comprehensive command line interface for managing admin users
 */

import { program } from 'commander';
import bcrypt from 'bcrypt';
import crypto from 'crypto';
import fs from 'fs';
import pkg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Client } = pkg;

// Constants
const VALID_ROLES = ['super_admin', 'admin', 'moderator', 'viewer'];
const DEFAULT_ROLE = 'admin';
const SALT_ROUNDS = 12;

// Utility Functions
function validatePassword(password) {
    const minLength = 12;
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
    
    const errors = [];
    if (password.length < minLength) errors.push(`Password must be at least ${minLength} characters long`);
    if (!hasUpper) errors.push('Password must contain at least one uppercase letter');
    if (!hasLower) errors.push('Password must contain at least one lowercase letter');
    if (!hasNumber) errors.push('Password must contain at least one number');
    if (!hasSpecial) errors.push('Password must contain at least one special character');
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validateRole(role) {
    return VALID_ROLES.includes(role);
}

async function hashPassword(password) {
    return bcrypt.hash(password, SALT_ROUNDS);
}

function generateSecurePassword(length = 16) {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
    let password = '';
    
    // Ensure at least one of each required character type
    password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // uppercase
    password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // lowercase
    password += '0123456789'[Math.floor(Math.random() * 10)]; // number
    password += '!@#$%^&*()_+-=[]{}|;:,.<>?'[Math.floor(Math.random() * 25)]; // special
    
    // Fill the rest randomly
    for (let i = password.length; i < length; i++) {
        password += charset[Math.floor(Math.random() * charset.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
}

function generateSessionSecrets() {
    const sessionSecret = crypto.randomBytes(32).toString('hex');
    const cookieSecret = crypto.randomBytes(32).toString('hex');
    return { sessionSecret, cookieSecret };
}

function saveToFile(content, filename) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const finalFilename = filename || `admin-user-${timestamp}.sql`;
    
    fs.writeFileSync(finalFilename, content);
    return finalFilename;
}

// Database Connection Functions
async function connectToDatabase() {
    const client = new Client({
        host: process.env.POSTGRES_HOST || 'localhost',
        port: parseInt(process.env.POSTGRES_PORT) || 5432,
        user: process.env.POSTGRES_USER || 'letzai',
        password: process.env.POSTGRES_PASSWORD || 'letzai',
        database: process.env.POSTGRES_DB || 'letzai',
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
    });
    
    try {
        await client.connect();
        console.log('🔌 Connected to database');
        return client;
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        throw error;
    }
}

async function ensureAdminUserTableExists(client) {
    const createTableQuery = `
        CREATE TABLE IF NOT EXISTS admin_user (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            role VARCHAR(50) NOT NULL DEFAULT 'viewer',
            status VARCHAR(50) NOT NULL DEFAULT 'active',
            two_factor_secret VARCHAR(255),
            two_factor_enabled BOOLEAN DEFAULT FALSE,
            allowed_ip_addresses JSONB,
            last_login_at TIMESTAMP,
            last_login_ip VARCHAR(45),
            failed_login_attempts INTEGER DEFAULT 0,
            locked_until TIMESTAMP,
            password_changed_at TIMESTAMP,
            must_change_password BOOLEAN DEFAULT TRUE,
            permissions JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            deleted_at TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_admin_email ON admin_user(email);
        CREATE INDEX IF NOT EXISTS idx_admin_status ON admin_user(status);
    `;
    
    try {
        await client.query(createTableQuery);
        console.log('✅ Admin user table verified/created');
    } catch (error) {
        console.error('❌ Failed to create admin user table:', error.message);
        throw error;
    }
}

// Database Operation Functions
async function createUserInDatabase(name, email, password, role = DEFAULT_ROLE) {
    // Validation
    const validation = validatePassword(password);
    if (!validation.isValid) {
        throw new Error(`Password validation failed:\n${validation.errors.map(e => `  - ${e}`).join('\n')}`);
    }
    
    if (!validateEmail(email)) {
        throw new Error('Invalid email format');
    }
    
    if (!validateRole(role)) {
        throw new Error(`Invalid role. Must be one of: ${VALID_ROLES.join(', ')}`);
    }
    
    const client = await connectToDatabase();
    
    try {
        await ensureAdminUserTableExists(client);
        
        const userId = crypto.randomUUID();
        const passwordHash = await hashPassword(password);
        
        const insertQuery = `
            INSERT INTO admin_user (
                id, name, email, password_hash, role, status, 
                must_change_password, password_changed_at, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, 'active', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON CONFLICT (email) DO UPDATE SET
                name = EXCLUDED.name,
                password_hash = EXCLUDED.password_hash,
                role = EXCLUDED.role,
                updated_at = CURRENT_TIMESTAMP
            RETURNING id, name, email, role, status, created_at;
        `;
        
        const result = await client.query(insertQuery, [userId, name, email.toLowerCase(), passwordHash, role]);
        
        console.log('✅ User created/updated successfully');
        return result.rows[0];
        
    } finally {
        await client.end();
        console.log('🔌 Database connection closed');
    }
}

async function changeUserPasswordInDatabase(email, newPassword) {
    const validation = validatePassword(newPassword);
    if (!validation.isValid) {
        throw new Error(`Password validation failed:\n${validation.errors.map(e => `  - ${e}`).join('\n')}`);
    }
    
    if (!validateEmail(email)) {
        throw new Error('Invalid email format');
    }
    
    const client = await connectToDatabase();
    
    try {
        const passwordHash = await hashPassword(newPassword);
        
        const updateQuery = `
            UPDATE admin_user 
            SET 
                password_hash = $1,
                must_change_password = FALSE,
                password_changed_at = CURRENT_TIMESTAMP,
                failed_login_attempts = 0,
                locked_until = NULL,
                updated_at = CURRENT_TIMESTAMP
            WHERE email = $2
            RETURNING email, password_changed_at, must_change_password;
        `;
        
        const result = await client.query(updateQuery, [passwordHash, email.toLowerCase()]);
        
        if (result.rows.length === 0) {
            throw new Error(`User with email ${email} not found`);
        }
        
        console.log('✅ Password changed successfully');
        return result.rows[0];
        
    } finally {
        await client.end();
        console.log('🔌 Database connection closed');
    }
}

async function lockUserInDatabase(email) {
    if (!validateEmail(email)) {
        throw new Error('Invalid email format');
    }
    
    const client = await connectToDatabase();
    
    try {
        const updateQuery = `
            UPDATE admin_user 
            SET 
                status = 'locked',
                locked_until = CURRENT_TIMESTAMP + INTERVAL '24 hours',
                updated_at = CURRENT_TIMESTAMP
            WHERE email = $1
            RETURNING email, status, locked_until;
        `;
        
        const result = await client.query(updateQuery, [email.toLowerCase()]);
        
        if (result.rows.length === 0) {
            throw new Error(`User with email ${email} not found`);
        }
        
        console.log('🔒 User locked successfully');
        return result.rows[0];
        
    } finally {
        await client.end();
        console.log('🔌 Database connection closed');
    }
}

async function unlockUserInDatabase(email) {
    if (!validateEmail(email)) {
        throw new Error('Invalid email format');
    }
    
    const client = await connectToDatabase();
    
    try {
        const updateQuery = `
            UPDATE admin_user 
            SET 
                status = 'active',
                failed_login_attempts = 0,
                locked_until = NULL,
                updated_at = CURRENT_TIMESTAMP
            WHERE email = $1
            RETURNING email, status, failed_login_attempts, locked_until;
        `;
        
        const result = await client.query(updateQuery, [email.toLowerCase()]);
        
        if (result.rows.length === 0) {
            throw new Error(`User with email ${email} not found`);
        }
        
        console.log('🔓 User unlocked successfully');
        return result.rows[0];
        
    } finally {
        await client.end();
        console.log('🔌 Database connection closed');
    }
}

async function deleteUserInDatabase(email, hardDelete = false) {
    if (!validateEmail(email)) {
        throw new Error('Invalid email format');
    }
    
    const client = await connectToDatabase();
    
    try {
        let updateQuery, result;
        
        if (hardDelete) {
            updateQuery = `DELETE FROM admin_user WHERE email = $1 RETURNING email;`;
            result = await client.query(updateQuery, [email.toLowerCase()]);
            console.log('🗑️ User permanently deleted');
        } else {
            updateQuery = `
                UPDATE admin_user 
                SET 
                    status = 'deleted',
                    deleted_at = CURRENT_TIMESTAMP,
                    updated_at = CURRENT_TIMESTAMP
                WHERE email = $1
                RETURNING email, status, deleted_at;
            `;
            result = await client.query(updateQuery, [email.toLowerCase()]);
            console.log('🗑️ User soft deleted');
        }
        
        if (result.rows.length === 0) {
            throw new Error(`User with email ${email} not found`);
        }
        
        return result.rows[0];
        
    } finally {
        await client.end();
        console.log('🔌 Database connection closed');
    }
}

async function getUserFromDatabase(email) {
    if (!validateEmail(email)) {
        throw new Error('Invalid email format');
    }
    
    const client = await connectToDatabase();
    
    try {
        const selectQuery = `
            SELECT id, name, email, role, status, created_at, updated_at, 
                   last_login_at, failed_login_attempts, locked_until, deleted_at
            FROM admin_user 
            WHERE email = $1;
        `;
        
        const result = await client.query(selectQuery, [email.toLowerCase()]);
        
        if (result.rows.length === 0) {
            return null;
        }
        
        return result.rows[0];
        
    } finally {
        await client.end();
    }
}

// SQL Generation Functions
async function generateCreateUserSQL(name, email, password, role = DEFAULT_ROLE) {
    const validation = validatePassword(password);
    if (!validation.isValid) {
        throw new Error(`Password validation failed:\n${validation.errors.map(e => `  - ${e}`).join('\n')}`);
    }
    
    if (!validateEmail(email)) {
        throw new Error('Invalid email format');
    }
    
    if (!validateRole(role)) {
        throw new Error(`Invalid role. Must be one of: ${VALID_ROLES.join(', ')}`);
    }
    
    const userId = crypto.randomUUID();
    const passwordHash = await hashPassword(password);
    
    return `-- Create admin user: ${name} (${email})
-- Generated: ${new Date().toISOString()}

INSERT INTO admin_user (
    id,
    name, 
    email, 
    password_hash, 
    role, 
    status, 
    must_change_password,
    password_changed_at,
    created_at,
    updated_at
) VALUES (
    '${userId}',
    '${name.replace(/'/g, "''")}',
    '${email.toLowerCase()}',
    '${passwordHash}',
    '${role}',
    'active',
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO UPDATE SET
    name = EXCLUDED.name,
    password_hash = EXCLUDED.password_hash,
    role = EXCLUDED.role,
    updated_at = CURRENT_TIMESTAMP;

-- Verify user creation
SELECT id, name, email, role, status, created_at 
FROM admin_user 
WHERE email = '${email.toLowerCase()}';
`;
}

function generateChangePasswordSQL(email, newPassword) {
    return `-- Change password for user: ${email}
-- Generated: ${new Date().toISOString()}

UPDATE admin_user 
SET 
    password_hash = '${newPassword}',
    must_change_password = FALSE,
    password_changed_at = CURRENT_TIMESTAMP,
    failed_login_attempts = 0,
    locked_until = NULL,
    updated_at = CURRENT_TIMESTAMP
WHERE email = '${email.toLowerCase()}';

-- Verify password change
SELECT email, password_changed_at, must_change_password
FROM admin_user 
WHERE email = '${email.toLowerCase()}';
`;
}

function generateLockUserSQL(email) {
    return `-- Lock user account: ${email}
-- Generated: ${new Date().toISOString()}

UPDATE admin_user 
SET 
    status = 'locked',
    locked_until = CURRENT_TIMESTAMP + INTERVAL '24 hours',
    updated_at = CURRENT_TIMESTAMP
WHERE email = '${email.toLowerCase()}';

-- Verify user lock
SELECT email, status, locked_until
FROM admin_user 
WHERE email = '${email.toLowerCase()}';
`;
}

function generateUnlockUserSQL(email) {
    return `-- Unlock user account: ${email}
-- Generated: ${new Date().toISOString()}

UPDATE admin_user 
SET 
    status = 'active',
    failed_login_attempts = 0,
    locked_until = NULL,
    updated_at = CURRENT_TIMESTAMP
WHERE email = '${email.toLowerCase()}';

-- Verify user unlock
SELECT email, status, failed_login_attempts, locked_until
FROM admin_user 
WHERE email = '${email.toLowerCase()}';
`;
}

function generateDeleteUserSQL(email) {
    return `-- Delete user account: ${email}
-- Generated: ${new Date().toISOString()}
-- WARNING: This will permanently delete the user account

-- Soft delete (recommended)
UPDATE admin_user 
SET 
    status = 'deleted',
    deleted_at = CURRENT_TIMESTAMP,
    updated_at = CURRENT_TIMESTAMP
WHERE email = '${email.toLowerCase()}';

-- Hard delete (uncomment if needed - IRREVERSIBLE)
-- DELETE FROM admin_user WHERE email = '${email.toLowerCase()}';

-- Verify deletion
SELECT email, status, deleted_at
FROM admin_user 
WHERE email = '${email.toLowerCase()}';
`;
}

// CLI Setup
program
    .name('admin')
    .description('Letz.ai Admin User Management CLI')
    .version('2.0.0');

// Create User Command
program
    .command('create')
    .description('Create a new admin user')
    .requiredOption('-n, --name <name>', 'User full name')
    .requiredOption('-e, --email <email>', 'User email address')
    .option('-p, --password <password>', 'User password (will generate secure one if not provided)')
    .option('-r, --role <role>', `User role (${VALID_ROLES.join(', ')})`, DEFAULT_ROLE)
    .option('--show-password', 'Show generated password in output', false)
    .action(async (options) => {
        try {
            console.log('🔐 Creating admin user...\n');
            
            // Generate password if not provided
            const password = options.password || generateSecurePassword(16);
            const isGenerated = !options.password;
            
            // Create user in database
            const user = await createUserInDatabase(
                options.name,
                options.email,
                password,
                options.role
            );
            
            // Show user details
            console.log('\n👤 User Details:');
            console.log(`   ID: ${user.id}`);
            console.log(`   Name: ${user.name}`);
            console.log(`   Email: ${user.email}`);
            console.log(`   Role: ${user.role}`);
            console.log(`   Status: ${user.status}`);
            console.log(`   Created: ${user.created_at}`);
            
            if (isGenerated && options.showPassword) {
                console.log(`   Password: ${password}`);
                console.log('   ⚠️  Save this password securely - it won\'t be shown again!');
            } else if (isGenerated) {
                console.log('   Password: [Generated - run with --show-password to display]');
            }
            
            console.log('\n✅ User successfully created and ready to use!');
            console.log('\n📋 Next steps:');
            console.log('   1. Test login with the new credentials');
            console.log('   2. Enable 2FA for enhanced security');
            
        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    });

// Change Password Command
program
    .command('change-password')
    .description('Change user password')
    .requiredOption('-e, --email <email>', 'User email address')
    .option('-p, --password <password>', 'New password (will generate secure one if not provided)')
    .option('--show-password', 'Show generated password in output', false)
    .action(async (options) => {
        try {
            console.log('🔑 Changing user password...\n');
            
            const password = options.password || generateSecurePassword(16);
            const isGenerated = !options.password;
            
            // Change password in database
            const result = await changeUserPasswordInDatabase(options.email, password);
            
            console.log('\n👤 Password Changed:');
            console.log(`   Email: ${result.email}`);
            console.log(`   Changed At: ${result.password_changed_at}`);
            console.log(`   Must Change Password: ${result.must_change_password}`);
            
            if (isGenerated && options.showPassword) {
                console.log(`   New Password: ${password}`);
                console.log('   ⚠️  Save this password securely!');
            } else if (isGenerated) {
                console.log('   New Password: [Generated - run with --show-password to display]');
            }
            
            console.log('\n✅ Password successfully changed!');
            
        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    });

// Lock User Command
program
    .command('lock')
    .description('Lock a user account')
    .requiredOption('-e, --email <email>', 'User email address')
    .action(async (options) => {
        try {
            console.log('🔒 Locking user account...\n');
            
            // Lock user in database
            const result = await lockUserInDatabase(options.email);
            
            console.log('\n🔒 User Locked:');
            console.log(`   Email: ${result.email}`);
            console.log(`   Status: ${result.status}`);
            console.log(`   Locked Until: ${result.locked_until}`);
            
            console.log('\n✅ User successfully locked for 24 hours!');
            
        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    });

// Unlock User Command
program
    .command('unlock')
    .description('Unlock a user account')
    .requiredOption('-e, --email <email>', 'User email address')
    .action(async (options) => {
        try {
            console.log('🔓 Unlocking user account...\n');
            
            // Unlock user in database
            const result = await unlockUserInDatabase(options.email);
            
            console.log('\n🔓 User Unlocked:');
            console.log(`   Email: ${result.email}`);
            console.log(`   Status: ${result.status}`);
            console.log(`   Failed Login Attempts: ${result.failed_login_attempts}`);
            console.log(`   Locked Until: ${result.locked_until || 'Not locked'}`);
            
            console.log('\n✅ User successfully unlocked!');
            
        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    });

// Delete User Command
program
    .command('delete')
    .description('Delete a user account (soft delete by default)')
    .requiredOption('-e, --email <email>', 'User email address')
    .option('--hard', 'Perform hard delete (irreversible)', false)
    .action(async (options) => {
        try {
            console.log('🗑️  Deleting user account...\n');
            
            if (options.hard) {
                console.log('⚠️  WARNING: Hard delete requested - this is IRREVERSIBLE!');
                console.log('This will permanently remove all user data.\n');
            }
            
            // Delete user in database
            const result = await deleteUserInDatabase(options.email, options.hard);
            
            console.log('\n🗑️ User Deleted:');
            console.log(`   Email: ${result.email}`);
            
            if (!options.hard) {
                console.log(`   Status: ${result.status}`);
                console.log(`   Deleted At: ${result.deleted_at}`);
                console.log('\nℹ️  This was a soft delete - user can be restored if needed');
            } else {
                console.log('\n⚠️  User permanently deleted from database!');
            }
            
            console.log('\n✅ User deletion completed!');
            
        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    });

// Utility Commands
program
    .command('generate-password')
    .description('Generate a secure password')
    .option('-l, --length <length>', 'Password length (minimum 12)', '16')
    .action((options) => {
        const length = Math.max(12, parseInt(options.length));
        const password = generateSecurePassword(length);
        
        console.log('🔑 Generated secure password:');
        console.log(`   ${password}`);
        console.log('\n✅ This password meets all security requirements');
        
        const validation = validatePassword(password);
        console.log(`   Length: ${password.length} characters`);
        console.log(`   Uppercase: ✓  Lowercase: ✓  Numbers: ✓  Special: ✓`);
    });

program
    .command('generate-secrets')
    .description('Generate session and cookie secrets')
    .action(() => {
        const { sessionSecret, cookieSecret } = generateSessionSecrets();
        
        console.log('🔐 Generated security secrets:');
        console.log('\nAdd these to your .env file:');
        console.log('=' .repeat(50));
        console.log(`SESSION_SECRET=${sessionSecret}`);
        console.log(`COOKIE_SECRET=${cookieSecret}`);
        console.log('=' .repeat(50));
        console.log('\n⚠️  Keep these secrets secure and don\'t commit them to version control!');
    });

// Help command customization
program.configureHelp({
    sortSubcommands: true,
    subcommandTerm: (cmd) => cmd.name() + ' ' + cmd.usage()
});

// Add help examples
program.addHelpText('after', `

Examples:
  $ npm run admin create -n "John Doe" -e "<EMAIL>" -r admin
  $ npm run admin change-password -e "<EMAIL>" --show-password
  $ npm run admin lock -e "<EMAIL>"
  $ npm run admin unlock -e "<EMAIL>"
  $ npm run admin delete -e "<EMAIL>"
  $ npm run admin generate-password -l 20
  $ npm run admin generate-secrets

Available roles: ${VALID_ROLES.join(', ')}
Default role: ${DEFAULT_ROLE}
`);

// Parse and execute
program.parse();
