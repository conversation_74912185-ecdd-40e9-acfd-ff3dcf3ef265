#!/bin/sh
set -e

echo "⚡ Starting LetzAI Admin (Simple Mode - Ultra Fast)..."

# Quick environment check
if [ ! -f "dist/simple-main.js" ]; then
    echo "❌ Error: dist/simple-main.js not found. Build may have failed."
    exit 1
fi

# Log startup info
echo "📊 Container Memory: ${CONTAINER_MEMORY_MB:-512}MB"
echo "🌐 Port: ${PORT:-3013}"
echo "⚡ Simple Mode: No AdminJS - Ultra Fast Startup"

# Execute the command
echo "🎯 Starting application..."
exec "$@"
