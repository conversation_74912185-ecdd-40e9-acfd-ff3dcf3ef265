#!/usr/bin/env node

/**
 * Create Admin User Script
 * 
 * This script creates admin users directly in the database
 * Works with the current setup while Node.js compatibility is being resolved
 */

import bcrypt from 'bcrypt';
import crypto from 'crypto';
import readline from 'readline';
import { fileURLToPath } from 'url';
import path from 'path';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(query) {
    return new Promise(resolve => rl.question(query, resolve));
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePassword(password) {
    const minLength = 12;
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
    
    const errors = [];
    if (password.length < minLength) errors.push(`Password must be at least ${minLength} characters long`);
    if (!hasUpper) errors.push('Password must contain at least one uppercase letter');
    if (!hasLower) errors.push('Password must contain at least one lowercase letter');
    if (!hasNumber) errors.push('Password must contain at least one number');
    if (!hasSpecial) errors.push('Password must contain at least one special character');
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

async function hashPassword(password) {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
}

async function createAdminUser() {
    console.log('🔐 Create New Admin User');
    console.log('========================\n');
    
    // Get user details
    let name, email, password, role;
    
    // Name
    name = await question('Enter admin name: ');
    while (!name.trim()) {
        name = await question('Name cannot be empty. Enter admin name: ');
    }
    
    // Email
    email = await question('Enter admin email: ');
    while (!validateEmail(email)) {
        email = await question('Invalid email format. Enter admin email: ');
    }
    
    // Role
    console.log('\nAvailable roles:');
    console.log('1. super_admin - Full system access');
    console.log('2. admin - Most administrative functions');
    console.log('3. moderator - Content moderation and user management');
    console.log('4. viewer - Read-only access');
    
    const roleChoice = await question('\nSelect role (1-4): ');
    const roles = {
        '1': 'super_admin',
        '2': 'admin', 
        '3': 'moderator',
        '4': 'viewer'
    };
    role = roles[roleChoice] || 'viewer';
    
    // Password
    console.log('\nPassword requirements:');
    console.log('- At least 12 characters long');
    console.log('- Contains uppercase and lowercase letters');
    console.log('- Contains at least one number');
    console.log('- Contains at least one special character\n');
    
    password = await question('Enter admin password: ');
    let passwordValidation = validatePassword(password);
    
    while (!passwordValidation.isValid) {
        console.log('\n❌ Password validation failed:');
        passwordValidation.errors.forEach(error => console.log(`   - ${error}`));
        console.log('');
        password = await question('Enter admin password: ');
        passwordValidation = validatePassword(password);
    }
    
    console.log('\n✅ Password meets security requirements.');
    
    // Hash password
    console.log('\n🔒 Hashing password...');
    const passwordHash = await hashPassword(password);
    
    // Generate user ID
    const userId = crypto.randomUUID();
    
    // Create SQL
    const sql = `
-- Create admin user: ${name} (${email})
INSERT INTO admin_user (
    id,
    name, 
    email, 
    password_hash, 
    role, 
    status, 
    must_change_password,
    password_changed_at,
    created_at,
    updated_at
) VALUES (
    '${userId}',
    '${name.replace(/'/g, "''")}',
    '${email.toLowerCase()}',
    '${passwordHash}',
    '${role}',
    'active',
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO UPDATE SET
    name = EXCLUDED.name,
    password_hash = EXCLUDED.password_hash,
    role = EXCLUDED.role,
    updated_at = CURRENT_TIMESTAMP;
`;
    
    // Save SQL file
    const sqlFile = path.join(__dirname, '..', `create-admin-${email.replace('@', '-at-').replace(/[^a-zA-Z0-9-]/g, '')}.sql`);
    
    console.log('\n📝 Generated SQL script:');
    console.log('='.repeat(50));
    console.log(sql);
    console.log('='.repeat(50));
    
    // Save to file
    const fs = await import('fs');
    fs.writeFileSync(sqlFile, sql);
    
    console.log(`\n✅ SQL script saved to: ${sqlFile}`);
    console.log('\n📋 Next steps:');
    console.log('1. Run the SQL script against your database');
    console.log('2. Or copy the SQL and execute it manually');
    console.log('3. The user can now login with the provided credentials');
    
    console.log('\n👤 User Details:');
    console.log(`   Name: ${name}`);
    console.log(`   Email: ${email}`);
    console.log(`   Role: ${role}`);
    console.log(`   Password: [HIDDEN - use the one you entered]`);
    
    rl.close();
}

// Run the script
createAdminUser().catch(error => {
    console.error('❌ Error creating admin user:', error.message);
    process.exit(1);
});
