#!/bin/bash
set -e

# Docker build optimization script for LetzAI Admin
echo "🐳 LetzAI Admin Docker Build Optimization Script"
echo "================================================"

# Default values
IMAGE_NAME="letzai-admin"
TAG="latest"
MODE="production"
PLATFORM="linux/amd64"
PUSH=false
REGISTRY=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        --tag)
            TAG="$2"
            shift 2
            ;;
        --mode)
            MODE="$2"
            shift 2
            ;;
        --platform)
            PLATFORM="$2"
            shift 2
            ;;
        --push)
            PUSH=true
            shift
            ;;
        --registry)
            REGISTRY="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --name NAME        Image name (default: letzai-admin)"
            echo "  --tag TAG          Image tag (default: latest)"
            echo "  --mode MODE        Build mode: production|simple|minimal (default: production)"
            echo "  --platform PLATFORM Target platform (default: linux/amd64)"
            echo "  --push             Push to registry after build"
            echo "  --registry URL     Registry URL for push"
            echo "  --help             Show this help"
            echo ""
            echo "Examples:"
            echo "  $0 --mode production --tag v1.0.0"
            echo "  $0 --mode simple --name letzai-simple --push --registry my-registry.com"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Set full image name
if [ -n "$REGISTRY" ]; then
    FULL_IMAGE_NAME="$REGISTRY/$IMAGE_NAME:$TAG"
else
    FULL_IMAGE_NAME="$IMAGE_NAME:$TAG"
fi

# Choose Dockerfile based on mode
if [ "$MODE" = "simple" ]; then
    DOCKERFILE="Dockerfile.simple"
    echo "🏃 Building in SIMPLE mode (ultra-fast startup, no AdminJS)"
elif [ "$MODE" = "minimal" ]; then
    DOCKERFILE="Dockerfile.minimal"
    echo "⚡ Building in MINIMAL mode (fastest build, no external packages)"
else
    DOCKERFILE="Dockerfile"
    echo "🏗️  Building in PRODUCTION mode (full features with AdminJS)"
fi

# Verify Dockerfile exists
if [ ! -f "$DOCKERFILE" ]; then
    echo "❌ Error: $DOCKERFILE not found"
    exit 1
fi

echo "📋 Build Configuration:"
echo "   Image: $FULL_IMAGE_NAME"
echo "   Mode: $MODE"
echo "   Platform: $PLATFORM"
echo "   Dockerfile: $DOCKERFILE"
echo "   Push: $PUSH"
echo ""

# Build the image
echo "🔨 Building Docker image..."
docker build \
    --platform "$PLATFORM" \
    --file "$DOCKERFILE" \
    --tag "$FULL_IMAGE_NAME" \
    --progress=plain \
    .

if [ $? -eq 0 ]; then
    echo "✅ Build completed successfully!"
    
    # Show image size
    echo "📊 Image size:"
    docker images "$FULL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
    
    # Push if requested
    if [ "$PUSH" = true ]; then
        echo "📤 Pushing to registry..."
        docker push "$FULL_IMAGE_NAME"
        if [ $? -eq 0 ]; then
            echo "✅ Push completed successfully!"
        else
            echo "❌ Push failed!"
            exit 1
        fi
    fi
    
    echo ""
    echo "🚀 Ready to run:"
    echo "   docker run -p 3013:3013 $FULL_IMAGE_NAME"
    echo ""
    if [ "$MODE" = "simple" ]; then
        echo "⚡ Simple mode: Ultra-fast startup (~1-2 seconds)"
        echo "🚫 No AdminJS panel available in simple mode"
    elif [ "$MODE" = "minimal" ]; then
        echo "⚡ Minimal mode: Fastest build and startup (~1-3 seconds)"
        echo "🔐 AdminJS panel available at /admin"
        echo "📦 No external packages - works even with network issues"
    else
        echo "🎯 Production mode: Fast startup (~3-5 seconds)"
        echo "🔐 AdminJS panel available at /admin"
    fi
else
    echo "❌ Build failed!"
    exit 1
fi
