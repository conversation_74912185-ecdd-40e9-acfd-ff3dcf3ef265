#!/bin/sh
set -e

echo "⚡ Starting LetzAI Admin (Pre-built Mode - Ultra Fast)..."

# Verify required files exist
if [ ! -f "dist/main.js" ]; then
    echo "❌ Error: dist/main.js not found. Run 'npm run build' first!"
    exit 1
fi

if [ ! -d "node_modules" ]; then
    echo "❌ Error: node_modules not found. Run 'npm install' first!"
    exit 1
fi

# Log startup info
echo "🌐 Port: ${PORT:-3013}"
echo "⚡ Pre-built Mode: No npm install, instant startup"
echo "📦 Using existing node_modules and dist"

# Execute directly for maximum speed
exec "$@"
