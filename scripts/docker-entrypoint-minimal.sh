#!/bin/sh
set -e

echo "🚀 Starting LetzAI Admin (Minimal Production Mode)..."

# Quick environment check
if [ "$NODE_ENV" != "production" ]; then
    echo "⚠️  Warning: NODE_ENV is not set to production"
fi

# Verify required files exist
if [ ! -f "dist/main.js" ]; then
    echo "❌ Error: dist/main.js not found. Build may have failed."
    exit 1
fi

# Log startup info
echo "📊 Container Memory: ${CONTAINER_MEMORY_MB:-1024}MB"
echo "🔧 Node Options: ${NODE_OPTIONS}"
echo "🌐 Port: ${PORT:-3013}"
echo "⚡ Minimal Mode: No external packages, fastest build"

# Execute the command directly
echo "🎯 Starting application..."
exec "$@"
