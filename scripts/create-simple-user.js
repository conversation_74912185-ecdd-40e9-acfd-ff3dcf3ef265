#!/usr/bin/env node

/**
 * Simple User Creation Script
 * Works with the existing user_account table
 */

import crypto from 'crypto';
import readline from 'readline';

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(query) {
    return new Promise(resolve => rl.question(query, resolve));
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

async function createSimpleUser() {
    console.log('👤 Create New User (Simple)');
    console.log('===========================\n');
    
    const name = await question('Enter user name: ');
    let email = await question('Enter user email: ');
    
    while (!validateEmail(email)) {
        email = await question('Invalid email. Enter user email: ');
    }
    
    const username = await question('Enter username (optional): ') || email.split('@')[0];
    
    const userId = crypto.randomUUID();
    
    const sql = `
-- Create user: ${name} (${email})
INSERT INTO user_account (
    id,
    name,
    email,
    username,
    is_active,
    is_verified,
    created_at,
    updated_at
) VALUES (
    '${userId}',
    '${name.replace(/'/g, "''")}',
    '${email.toLowerCase()}',
    '${username.replace(/'/g, "''")}',
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO UPDATE SET
    name = EXCLUDED.name,
    username = EXCLUDED.username,
    updated_at = CURRENT_TIMESTAMP;
`;
    
    console.log('\n📝 Generated SQL:');
    console.log('='.repeat(40));
    console.log(sql);
    console.log('='.repeat(40));
    
    console.log('\n✅ Copy and run this SQL in your database');
    
    rl.close();
}

createSimpleUser().catch(console.error);
