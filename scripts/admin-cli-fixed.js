#!/usr/bin/env node

/**
 * Admin CLI Tool - Fixed Version
 * Command line interface for managing admin users
 */

import { program } from 'commander';
import bcrypt from 'bcrypt';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

// Password validation
function validatePassword(password) {
    const minLength = 12;
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
    
    const errors = [];
    if (password.length < minLength) errors.push(`Password must be at least ${minLength} characters long`);
    if (!hasUpper) errors.push('Password must contain at least one uppercase letter');
    if (!hasLower) errors.push('Password must contain at least one lowercase letter');
    if (!hasNumber) errors.push('Password must contain at least one number');
    if (!hasSpecial) errors.push('Password must contain at least one special character');
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

// Hash password
async function hashPassword(password) {
    return bcrypt.hash(password, 12);
}

// Generate SQL for creating user
async function generateCreateUserSQL(name, email, password, role = 'admin') {
    const validation = validatePassword(password);
    if (!validation.isValid) {
        console.error('❌ Password validation failed:');
        validation.errors.forEach(error => console.error(`   - ${error}`));
        process.exit(1);
    }
    
    const userId = crypto.randomUUID();
    const passwordHash = await hashPassword(password);
    
    return `
-- Create admin user: ${name} (${email})
INSERT INTO admin_user (
    id,
    name, 
    email, 
    password_hash, 
    role, 
    status, 
    must_change_password,
    password_changed_at,
    created_at,
    updated_at
) VALUES (
    '${userId}',
    '${name.replace(/'/g, "''")}',
    '${email.toLowerCase()}',
    '${passwordHash}',
    '${role}',
    'active',
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO UPDATE SET
    name = EXCLUDED.name,
    password_hash = EXCLUDED.password_hash,
    role = EXCLUDED.role,
    updated_at = CURRENT_TIMESTAMP;
`;
}

// Set up the program
program
    .name('admin-cli')
    .description('Letz.ai Admin User Management CLI')
    .version('1.0.0');

// Create user command
program
    .command('create-user')
    .description('Create a new admin user')
    .requiredOption('-n, --name <name>', 'User full name')
    .requiredOption('-e, --email <email>', 'User email address')
    .requiredOption('-p, --password <password>', 'User password')
    .option('-r, --role <role>', 'User role (super_admin, admin, moderator, viewer)', 'admin')
    .option('-o, --output <file>', 'Output SQL file')
    .action(async (options) => {
        try {
            console.log('🔐 Creating admin user...');
            
            const sql = await generateCreateUserSQL(
                options.name,
                options.email,
                options.password,
                options.role
            );
            
            if (options.output) {
                fs.writeFileSync(options.output, sql);
                console.log(`✅ SQL script saved to: ${options.output}`);
            } else {
                console.log('\n📝 Generated SQL:');
                console.log('='.repeat(50));
                console.log(sql);
                console.log('='.repeat(50));
            }
            
            console.log('\n👤 User Details:');
            console.log(`   Name: ${options.name}`);
            console.log(`   Email: ${options.email}`);
            console.log(`   Role: ${options.role}`);
            
        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    });

// Generate password command
program
    .command('generate-password')
    .description('Generate a secure password')
    .option('-l, --length <length>', 'Password length', '16')
    .action((options) => {
        const length = parseInt(options.length);
        const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
        let password = '';
        
        // Ensure at least one of each required character type
        password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // uppercase
        password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // lowercase
        password += '0123456789'[Math.floor(Math.random() * 10)]; // number
        password += '!@#$%^&*()_+-=[]{}|;:,.<>?'[Math.floor(Math.random() * 25)]; // special
        
        // Fill the rest randomly
        for (let i = password.length; i < length; i++) {
            password += charset[Math.floor(Math.random() * charset.length)];
        }
        
        // Shuffle the password
        password = password.split('').sort(() => Math.random() - 0.5).join('');
        
        console.log('🔑 Generated secure password:');
        console.log(`   ${password}`);
        console.log('\n✅ This password meets all security requirements');
    });

// Hash password command
program
    .command('hash-password')
    .description('Hash a password for manual database insertion')
    .requiredOption('-p, --password <password>', 'Password to hash')
    .action(async (options) => {
        try {
            const validation = validatePassword(options.password);
            if (!validation.isValid) {
                console.error('❌ Password validation failed:');
                validation.errors.forEach(error => console.error(`   - ${error}`));
                process.exit(1);
            }
            
            const hash = await hashPassword(options.password);
            console.log('🔒 Password hash:');
            console.log(`   ${hash}`);
        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    });

program.parse();
