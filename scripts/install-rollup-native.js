#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to install the correct Rollup native binary for the current platform
 * This fixes the ARM64 compatibility issue in Docker containers
 */

const { execSync } = require('child_process');
const os = require('os');

function getRollupNativePackage() {
    const platform = os.platform();
    const arch = os.arch();
    
    console.log(`Detected platform: ${platform}, architecture: ${arch}`);
    
    // Map Node.js arch to Rollup package names
    const packageMap = {
        'linux': {
            'x64': '@rollup/rollup-linux-x64-gnu',
            'arm64': '@rollup/rollup-linux-arm64-gnu',
            'arm': '@rollup/rollup-linux-arm-gnueabihf'
        },
        'darwin': {
            'x64': '@rollup/rollup-darwin-x64',
            'arm64': '@rollup/rollup-darwin-arm64'
        },
        'win32': {
            'x64': '@rollup/rollup-win32-x64-msvc',
            'ia32': '@rollup/rollup-win32-ia32-msvc',
            'arm64': '@rollup/rollup-win32-arm64-msvc'
        }
    };
    
    // For Alpine Linux (musl), use musl variants
    const isAlpine = process.env.ALPINE_VERSION || 
                    (platform === 'linux' && execSync('ldd --version 2>&1 || true', { encoding: 'utf8' }).includes('musl'));
    
    if (isAlpine && platform === 'linux') {
        const muslPackageMap = {
            'x64': '@rollup/rollup-linux-x64-musl',
            'arm64': '@rollup/rollup-linux-arm64-musl',
            'arm': '@rollup/rollup-linux-arm-musleabihf'
        };
        return muslPackageMap[arch];
    }
    
    return packageMap[platform] && packageMap[platform][arch];
}

function installRollupNative() {
    try {
        const packageName = getRollupNativePackage();
        
        if (!packageName) {
            console.log(`No specific Rollup native package found for ${os.platform()}-${os.arch()}, using fallback`);
            return;
        }
        
        console.log(`Installing Rollup native package: ${packageName}`);
        
        // Try to install the specific native package
        try {
            execSync(`npm install ${packageName} --save-optional --no-audit --no-fund`, { 
                stdio: 'inherit',
                timeout: 60000 
            });
            console.log(`✅ Successfully installed ${packageName}`);
        } catch (installError) {
            console.log(`⚠️  Failed to install ${packageName}, trying alternative approach...`);
            
            // Alternative: try to install all possible native packages
            const allPackages = [
                '@rollup/rollup-linux-arm64-musl',
                '@rollup/rollup-linux-arm64-gnu',
                '@rollup/rollup-linux-x64-musl',
                '@rollup/rollup-linux-x64-gnu'
            ];
            
            for (const pkg of allPackages) {
                try {
                    execSync(`npm install ${pkg} --save-optional --no-audit --no-fund --ignore-scripts`, { 
                        stdio: 'pipe',
                        timeout: 30000 
                    });
                    console.log(`✅ Installed fallback package: ${pkg}`);
                } catch (e) {
                    // Ignore individual package failures
                }
            }
        }
        
    } catch (error) {
        console.error('Error installing Rollup native packages:', error.message);
        // Don't fail the build, just warn
        console.log('⚠️  Continuing without native Rollup packages - performance may be affected');
    }
}

// Run the installation
if (require.main === module) {
    installRollupNative();
}

module.exports = { installRollupNative, getRollupNativePackage };
