# Database Configuration
# POSTGRES_HOST=db
POSTGRES_HOST=127.0.0.1
POSTGRES_PORT=5432
POSTGRES_DB=letzai
POSTGRES_USER=letzai
POSTGRES_PASSWORD=letzai

# Legacy Admin Credentials (for fallback only)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin

# Application Configuration
PORT=3013
NODE_ENV=development

# API Configuration
API_URL=http://127.0.0.1:3000
API_KEY=changeme

# Security Configuration
# Generate these with: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
SESSION_SECRET=your-secure-session-secret-here
COOKIE_SECRET=your-secure-cookie-secret-here

# Security Features
ENFORCE_SESSION_IP=false
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15
SESSION_DURATION_HOURS=8

# IP Whitelist (comma-separated, leave empty to disable)
ALLOWED_IPS=

# Rate Limiting
RATE_LIMIT_WINDOW_MINUTES=15
RATE_LIMIT_MAX_REQUESTS=100
LOGIN_RATE_LIMIT_MAX_ATTEMPTS=5
