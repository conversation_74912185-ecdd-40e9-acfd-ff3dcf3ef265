# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs (will be built in container)
# Note: dist is excluded because we build it in the container

# Testing
.nyc_output
coverage
test/

# Git and documentation
.git
.gitignore
README.md
DOCKER_OPTIMIZATION.md
*.md

# IDE and OS
.DS_Store
.vscode/
.idea/
*.swp
*.swo

# Docker files
Dockerfile*
.dockerignore
docker-compose*.yml

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml

# Logs and temporary files
logs
*.log
tmp/
temp/
*.tmp
*.backup
*.bak

# Cache
.cache
.parcel-cache
*.tsbuildinfo
