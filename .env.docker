POSTGRES_HOST=host.docker.internal
POSTGRES_PORT=5433
POSTGRES_DB=letzai
POSTGRES_USER=letzai
POSTGRES_PASSWORD=letzai

API_URL=http://127.0.0.1:3000
API_KEY=changeme

ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin

PORT=3013

# Memory Management for Production
NODE_OPTIONS=--max-old-space-size=2048
ADMINJS_BUNDLE_TIMEOUT=300000
ADMINJS_BUNDLE_CACHE=true

# Session Management (Memory Store Optimized)
SESSION_CLEANUP_INTERVAL=300000
SESSION_MAX_AGE=7200000
SESSION_PRUNE_EXPIRED=true

# Required
HTTP_AUTH_USERNAME=letzai
HTTP_AUTH_PASSWORD=everything

# Optional
HTTP_AUTH_REALM=Letz.ai Admin Panel
HTTP_AUTH_ENABLED=false
SKIP_HTTP_AUTH_DEV=false

SESSION_SECRET=a0fdd3cdd710e3894258f020f277b893fac245fe39d88d27b24aa621c1543a19
COOKIE_SECRET=db5d06961b28702101fcafe041e1b20517eb918016f90bfff658a0db38173b00

NODE_ENV=development
