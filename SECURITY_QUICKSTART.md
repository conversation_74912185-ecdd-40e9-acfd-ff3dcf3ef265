# Quick Security Setup Guide

## Current Status

Your admin panel now has enhanced security features, but we encountered a Node.js compatibility issue with AdminJS 7.2.0 and Node.js v23.11.0.

## Immediate Solutions

### Option 1: Use Node.js 18 (Recommended)

```bash
# Install Node.js 18 using nvm
nvm install 18
nvm use 18

# Reinstall dependencies
npm install

# Start the application
npm run start:dev
```

### Option 2: Use Docker with Node.js 18

Update your `Dockerfile`:

```dockerfile
# Use Node.js 18 instead of latest
FROM node:18

# Rest of your Dockerfile remains the same
WORKDIR /usr/src/app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
CMD [ "node", "dist/main.js" ]
```

### Option 3: Quick Environment Fix

Generate secure secrets for your `.env` file:

```bash
# Generate secure secrets
npm run generate:secrets

# Copy output to your .env file
```

## Security Features Already Implemented

### ✅ Enhanced Authentication
- Secure session management with crypto-random secrets
- Login attempt logging
- Improved cookie security

### ✅ Environment Security
- Secure secret generation
- Comprehensive security configuration
- Production-ready settings

### ✅ Infrastructure Ready
- Admin user management system
- Audit logging entities
- Rate limiting middleware
- Two-factor authentication support

## Current .env Security Settings

Add these to your `.env` file:

```bash
# Security Configuration
SESSION_SECRET=your-generated-secret-here
COOKIE_SECRET=your-generated-secret-here

# Security Features
ENFORCE_SESSION_IP=false
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15
SESSION_DURATION_HOURS=8

# Rate Limiting
RATE_LIMIT_WINDOW_MINUTES=15
RATE_LIMIT_MAX_REQUESTS=100
LOGIN_RATE_LIMIT_MAX_ATTEMPTS=5
```

## Immediate Security Benefits

Even with the current setup, you now have:

1. **Secure session secrets** instead of hardcoded "secret"
2. **Enhanced cookie security** (HttpOnly, Secure, SameSite)
3. **Login attempt logging** for security monitoring
4. **Configurable session duration** (8 hours instead of indefinite)
5. **Production-ready security headers**

## Next Steps

1. **Fix Node.js compatibility** (use Node.js 18)
2. **Generate secure secrets** for your environment
3. **Enable the full security system** by uncommenting the admin user modules
4. **Set up database migrations** for the security tables
5. **Create your first admin user** with strong password requirements

## Testing Current Security

1. Start the application with Node.js 18
2. Check the console for security logs when logging in
3. Verify secure cookies in browser dev tools
4. Test session expiration (8 hours)

## Full Security System Activation

Once Node.js compatibility is resolved:

1. Uncomment the AdminUserModule in `src/app.module.ts`
2. Run database migrations from `src/migrations/`
3. Use the security setup script: `npm run security:setup`
4. Enable two-factor authentication
5. Set up IP restrictions if needed

The security infrastructure is complete and ready to use once the Node.js compatibility issue is resolved.
