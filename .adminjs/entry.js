AdminJS.UserComponents = {}
AdminJS.env.API_URL = "http://127.0.0.1:3000"
AdminJS.env.API_KEY = "changeme"
import ImageList from '../dist/components/ImageList'
AdminJS.UserComponents.ImageList = ImageList
import ImageDetail from '../dist/components/ImageDetail'
AdminJS.UserComponents.ImageDetail = ImageDetail
import ModelDetail from '../dist/components/ModelDetail'
AdminJS.UserComponents.ModelDetail = ModelDetail
import UserDetail from '../dist/components/UserDetail'
AdminJS.UserComponents.UserDetail = UserDetail
import UserDetailEdit from '../dist/components/UserDetailEdit'
AdminJS.UserComponents.UserDetailEdit = UserDetailEdit
import Statistics from '../dist/components/Statistics'
AdminJS.UserComponents.Statistics = Statistics
import OrganizationDetail from '../dist/components/OrganizationDetail'
AdminJS.UserComponents.OrganizationDetail = OrganizationDetail
import ChangePasswordForm from '../dist/components/ChangePasswordForm'
AdminJS.UserComponents.ChangePasswordForm = ChangePasswordForm