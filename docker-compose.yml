version: '3.8'

name: letzai-admin

services:
    admin:
        container_name: letzai-admin
        build:
            dockerfile: Dockerfile
            context: .
        env_file: .env.docker
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        ports:
            - '3013:3013'
        volumes:
            - .:/usr/src/app
        # Use the entrypoint script from Dockerfile
        command: sh -c 'chmod +x /usr/src/app/node_modules/.bin/* && npm run start:dev'

#     db:
#         container_name: letzai-admin-db
#         image: postgres:14-alpine
#         env_file: .env
#         ports:
#             - 5432
#         volumes:
#             - letzai-admin-dbdata:/var/lib/postgresql/data

# volumes:
#   letzai-admin-dbdata:
