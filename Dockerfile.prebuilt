# Pre-built container - copies everything as-is, no npm install
# Assumes you've already run: npm install && npm run build
FROM node:22-alpine

# Create app directory and non-root user
WORKDIR /usr/src/app
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Copy everything needed (pre-built)
COPY package.json ./
COPY dist ./dist
COPY node_modules ./node_modules

# Copy entrypoint
COPY scripts/docker-entrypoint-prebuilt.sh ./scripts/
RUN chmod +x scripts/docker-entrypoint-prebuilt.sh

# Change ownership to non-root user
RUN chown -R nextjs:nodejs /usr/src/app
USER nextjs

# Set production environment variables
ENV NODE_ENV=production
ENV PORT=3013
ENV NODE_OPTIONS="--max-old-space-size=512"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3013/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Direct execution for maximum speed
ENTRYPOINT ["/usr/src/app/scripts/docker-entrypoint-prebuilt.sh"]
CMD ["node", "dist/main.js"]
