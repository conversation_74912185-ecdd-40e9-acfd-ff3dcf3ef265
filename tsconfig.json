{"compilerOptions": {"allowSyntheticDefaultImports": true, "baseUrl": "./", "declaration": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": false, "incremental": true, "jsx": "react", "module": "nodenext", "moduleResolution": "nodenext", "noFallthroughCasesInSwitch": false, "noImplicitAny": false, "outDir": "./dist", "removeComments": true, "skipLibCheck": true, "sourceMap": true, "strictBindCallApply": false, "strictNullChecks": false, "target": "esnext"}}