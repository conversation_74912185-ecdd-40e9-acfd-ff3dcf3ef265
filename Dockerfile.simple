# Ultra-fast production container without AdminJS
# For when you need the fastest possible startup time
FROM node:22-alpine AS builder

# Update package index and install build dependencies
RUN apk update && apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++ \
    && ln -sf python3 /usr/bin/python

# Create app directory
WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --no-audit --no-fund

# Copy source code
COPY . .

# Build the application (without AdminJS)
RUN npm run build

# Production stage
FROM node:22-alpine AS production

# Update package index and install runtime dependencies
RUN apk update && apk add --no-cache \
    tini

# Create app directory and non-root user
WORKDIR /usr/src/app
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Copy package files
COPY package*.json ./

# Install only production dependencies (minimal set)
RUN npm install --omit=dev --no-audit --no-fund && \
    npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/certs ./certs

# Copy simple entrypoint
COPY scripts/docker-entrypoint-simple.sh ./scripts/
RUN chmod +x scripts/docker-entrypoint-simple.sh

# Change ownership to non-root user
RUN chown -R nextjs:nodejs /usr/src/app
USER nextjs

# Set production environment variables
ENV NODE_ENV=production
ENV PORT=3013
ENV NODE_OPTIONS="--max-old-space-size=512"
ENV CONTAINER_MEMORY_MB=512
ENV SIMPLE_MODE=true

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3013/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Use simple entrypoint
ENTRYPOINT ["tini", "--", "/usr/src/app/scripts/docker-entrypoint-simple.sh"]
CMD ["node", "dist/simple-main.js"]
