{"name": "letzai-admin", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "type": "module", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:prod:node18": "node --experimental-json-modules dist/main", "start:simple": "npx tsc src/simple-main.ts --outDir dist --module esnext --target esnext --moduleResolution node && node dist/simple-main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "security:setup": "node scripts/setup-security.js", "security:audit": "npm audit", "http-auth:generate": "npx tsx src/scripts/http-auth-setup.ts generate", "http-auth:config": "npx tsx src/scripts/http-auth-setup.ts config", "http-auth:test": "npx tsx src/scripts/http-auth-setup.ts test", "http-auth:disable": "npx tsx src/scripts/http-auth-setup.ts disable", "http-auth:enable": "npx tsx src/scripts/http-auth-setup.ts enable", "security:fix": "npm audit fix", "migration:run": "echo 'Run the SQL migration files in src/migrations/ against your database'", "generate:secrets": "node -e \"console.log('SESSION_SECRET=' + require('crypto').randomBytes(32).toString('hex')); console.log('COOKIE_SECRET=' + require('crypto').randomBytes(32).toString('hex'));\"", "create:admin": "node scripts/create-admin-user.js", "create:user": "node scripts/create-simple-user.js", "admin:cli": "node scripts/admin-cli-fixed.js", "quick:user": "node scripts/quick-user.js", "create:user:simple": "node scripts/create-user-simple.js"}, "dependencies": {"@adminjs/design-system": "^4.0.2", "@adminjs/express": "^6.0.0", "@adminjs/nestjs": "^6.0.1", "@adminjs/typeorm": "^5.0.0", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/typeorm": "^11.0.0", "@types/bcrypt": "^5.0.2", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "adminjs": "^7.2.0", "bcrypt": "^6.0.0", "chart.js": "^4.4.5", "commander": "^14.0.0", "dotenv": "^16.3.1", "express-formidable": "^1.2.0", "express-rate-limit": "^6.11.2", "express-session": "^1.17.3", "express-validator": "^7.2.1", "helmet": "^7.2.0", "pg": "^8.11.3", "qrcode": "^1.5.4", "react-chartjs-2": "^5.2.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "speakeasy": "^2.0.0", "typeorm": "~0.3.17", "typeorm-naming-strategies": "^4.1.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}