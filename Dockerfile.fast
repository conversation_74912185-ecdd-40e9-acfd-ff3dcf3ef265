# Ultra-fast production container - assumes pre-built dist folder
# Build locally first: npm run build
FROM node:22-alpine

# Create app directory and non-root user
WORKDIR /usr/src/app
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Copy only production files
COPY package.json ./
COPY dist ./dist

# Install only runtime dependencies (no build tools needed)
RUN npm install --production --no-audit --no-fund && \
    npm cache clean --force

# Copy fast entrypoint
COPY scripts/docker-entrypoint-fast.sh ./scripts/
RUN chmod +x scripts/docker-entrypoint-fast.sh

# Change ownership to non-root user
RUN chown -R nextjs:nodejs /usr/src/app
USER nextjs

# Set production environment variables
ENV NODE_ENV=production
ENV PORT=3013
ENV NODE_OPTIONS="--max-old-space-size=512"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3013/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Direct execution - no init system needed for speed
ENTRYPOINT ["/usr/src/app/scripts/docker-entrypoint-fast.sh"]
CMD ["node", "dist/main.js"]
