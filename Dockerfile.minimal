# Minimal production container - no additional packages
# This should work even with network issues
FROM node:22-alpine

# Create app directory and non-root user
WORKDIR /usr/src/app
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Copy package files and install ALL dependencies (needed for build)
COPY package.json ./
RUN npm install --no-audit --no-fund

# Copy source code and build
COPY . .
RUN npm run build

# Remove dev dependencies after build
RUN npm prune --omit=dev && \
    npm cache clean --force

# Copy production entrypoint and make executable
COPY scripts/docker-entrypoint-minimal.sh ./scripts/
RUN chmod +x scripts/docker-entrypoint-minimal.sh

# Change ownership to non-root user
RUN chown -R nextjs:nodejs /usr/src/app
USER nextjs

# Set production environment variables
ENV NODE_ENV=production
ENV PORT=3013
ENV NODE_OPTIONS="--max-old-space-size=1024"
ENV CONTAINER_MEMORY_MB=1024

# Disable AdminJS runtime bundling since we pre-built
ENV ADMINJS_BUNDLE_CACHE=true
ENV ADMINJS_SKIP_BUNDLE=true

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3013/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Use minimal entrypoint
ENTRYPOINT ["/usr/src/app/scripts/docker-entrypoint-minimal.sh"]
CMD ["node", "dist/main.js"]
