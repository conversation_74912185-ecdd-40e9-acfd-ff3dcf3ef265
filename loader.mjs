// Custom loader to handle import assertions for AdminJS compatibility
export async function resolve(specifier, context, defaultResolve) {
  return defaultResolve(specifier, context);
}

export async function load(url, context, defaultLoad) {
  const result = await defaultLoad(url, context);
  
  // Handle JSON imports with assertions
  if (url.endsWith('.json') && result.source) {
    const source = result.source.toString();
    return {
      format: 'json',
      source: source,
    };
  }
  
  return result;
}
