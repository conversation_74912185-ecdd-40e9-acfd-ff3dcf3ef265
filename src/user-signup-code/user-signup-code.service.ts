import { Injectable, Inject } from '@nestjs/common';
import { Repository } from 'typeorm';
import { SignupCode } from './user-signup-code.entity.js';

@Injectable()
export class SignupCodeService {
    constructor(
        @Inject('USER_SIGNUP_CODE_REPOSITORY')
        private signUpCodeRepository: Repository<SignupCode>,
    ) {}

    async findAll(): Promise<SignupCode[]> {
        return this.signUpCodeRepository.find();
    }
}
