import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
  } from 'typeorm';
  import { User } from '../users/user.entity.js';
  
  @Entity('user_signup_code')
  export class SignupCode extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;
  
    @ManyToOne(() => User, { eager: true })
    user: User;

    @Column({ nullable: true })
    userId?: string;
  
    @Column({ nullable: true, unique: true })
    code?: string;
  
    @Column({ type: 'text', nullable: true })
    notes?: string;
  
    @Column({ default: 0 })
    usages: number;
  
    @Column({ nullable: true })
    limit?: number;
  
    @Column({ default: true })
    isActive: boolean;
  
    @DeleteDateColumn({ type: 'timestamp', nullable: true })
    deletedAt?: Date;
  
    @CreateDateColumn()
    createdAt: Date;
  
    @UpdateDateColumn()
    updatedAt: Date;
  }