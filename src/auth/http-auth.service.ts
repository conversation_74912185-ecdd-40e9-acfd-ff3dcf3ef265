import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';

@Injectable()
export class HttpAuthService {
    private readonly defaultCredentials = {
        username: 'admin',
        password: 'secure123',
    };

    generateSecureCredentials(): { username: string; password: string } {
        const username = 'admin_' + crypto.randomBytes(4).toString('hex');
        const password = this.generateSecurePassword();
        
        return { username, password };
    }

    generateSecurePassword(length: number = 16): string {
        const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
        let password = '';
        
        // Ensure at least one of each type
        password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // uppercase
        password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // lowercase
        password += '0123456789'[Math.floor(Math.random() * 10)]; // number
        password += '!@#$%^&*'[Math.floor(Math.random() * 8)]; // special
        
        // Fill the rest
        for (let i = password.length; i < length; i++) {
            password += charset[Math.floor(Math.random() * charset.length)];
        }
        
        // Shuffle
        return password.split('').sort(() => Math.random() - 0.5).join('');
    }

    validateCredentials(username: string, password: string): boolean {
        const envUsername = process.env.HTTP_AUTH_USERNAME || this.defaultCredentials.username;
        const envPassword = process.env.HTTP_AUTH_PASSWORD || this.defaultCredentials.password;
        
        return username === envUsername && password === envPassword;
    }

    getAuthHeader(username?: string, password?: string): string {
        const user = username || process.env.HTTP_AUTH_USERNAME || this.defaultCredentials.username;
        const pass = password || process.env.HTTP_AUTH_PASSWORD || this.defaultCredentials.password;
        
        const credentials = Buffer.from(`${user}:${pass}`).toString('base64');
        return `Basic ${credentials}`;
    }

    isEnabled(): boolean {
        return process.env.HTTP_AUTH_ENABLED !== 'false';
    }

    getConfig() {
        return {
            enabled: this.isEnabled(),
            username: process.env.HTTP_AUTH_USERNAME || this.defaultCredentials.username,
            realm: process.env.HTTP_AUTH_REALM || 'Letz.ai Admin Panel',
            skipInDev: process.env.SKIP_HTTP_AUTH_DEV === 'true',
        };
    }
}
