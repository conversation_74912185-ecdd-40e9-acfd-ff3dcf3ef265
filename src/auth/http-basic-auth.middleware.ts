import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import * as crypto from 'crypto';

export interface HttpBasicAuthConfig {
    username: string;
    password: string;
    realm?: string;
    enabled?: boolean;
}

@Injectable()
export class HttpBasicAuthMiddleware implements NestMiddleware {
    protected readonly config: HttpBasicAuthConfig;

    constructor() {
        this.config = {
            username: process.env.HTTP_AUTH_USERNAME || 'admin',
            password: process.env.HTTP_AUTH_PASSWORD || 'secure123',
            realm: process.env.HTTP_AUTH_REALM || 'Letz.ai Admin Panel',
            enabled: process.env.HTTP_AUTH_ENABLED !== 'false', // Enabled by default
        };

        // Log configuration (without password)
        console.log('[HTTP_AUTH] Configuration:', {
            username: this.config.username,
            realm: this.config.realm,
            enabled: this.config.enabled,
            passwordSet: !!this.config.password,
        });
    }

    use(req: Request, res: Response, next: NextFunction) {
        const requestPath = req.path || req.url;
        const method = req.method;

        console.log(`[HTTP_AUTH] Processing request: ${method} ${requestPath}`);
        console.log(`[HTTP_AUTH] Config - Enabled: ${this.config.enabled}, Username: ${this.config.username}, Realm: ${this.config.realm}`);

        // Skip HTTP auth if disabled
        if (!this.config.enabled) {
            console.log('[HTTP_AUTH] HTTP auth is disabled, skipping');
            return next();
        }

        // Skip HTTP auth in development if specified
        if (process.env.NODE_ENV === 'development' && process.env.SKIP_HTTP_AUTH_DEV === 'true') {
            console.log('[HTTP_AUTH] Skipping HTTP auth in development mode');
            return next();
        }

        const authHeader = req.headers.authorization;

        if (!authHeader || !authHeader.startsWith('Basic ')) {
            return this.requestAuth(res);
        }

        try {
            // Extract and decode credentials
            const base64Credentials = authHeader.split(' ')[1];
            const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
            const [username, password] = credentials.split(':');

            // Validate credentials using constant-time comparison to prevent timing attacks
            const isValidUsername = this.constantTimeCompare(username, this.config.username);
            const isValidPassword = this.constantTimeCompare(password, this.config.password);

            if (isValidUsername && isValidPassword) {
                // Log successful HTTP auth
                const clientIp = req.ip || req.connection?.remoteAddress || 'unknown';
                console.log(`[HTTP_AUTH] Successful authentication for ${username} from ${clientIp}`);

                // Add HTTP auth info to request for logging
                (req as any).httpAuth = {
                    username,
                    authenticatedAt: new Date(),
                    clientIp,
                };

                return next();
            } else {
                // Log failed HTTP auth attempt
                const clientIp = req.ip || req.connection?.remoteAddress || 'unknown';
                console.log(`[HTTP_AUTH] Failed authentication attempt for ${username || 'unknown'} from ${clientIp}`);

                return this.requestAuth(res);
            }
        } catch (error) {
            console.error('[HTTP_AUTH] Error processing authentication:', error.message);
            return this.requestAuth(res);
        }
    }

    protected requestAuth(res: Response): void {
        res.setHeader('WWW-Authenticate', `Basic realm="${this.config.realm}"`);
        res.status(401).json({
            error: 'HTTP Authentication Required',
            message: 'Please provide valid credentials to access the admin panel',
        });
    }

    protected constantTimeCompare(a: string, b: string): boolean {
        if (a.length !== b.length) {
            return false;
        }

        let result = 0;
        for (let i = 0; i < a.length; i++) {
            result |= a.charCodeAt(i) ^ b.charCodeAt(i);
        }

        return result === 0;
    }
}


