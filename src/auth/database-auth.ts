import { DataSource } from 'typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import * as bcrypt from 'bcrypt';
import * as fs from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));

// Simple database authentication that works with current setup
export class DatabaseAuth {
    private dataSource: DataSource;

    constructor(dataSource: DataSource) {
        this.dataSource = dataSource;
    }

    async authenticate(email: string, password: string, context?: any): Promise<any> {
        const req = context?.request;
        const ipAddress = req?.ip || req?.connection?.remoteAddress || 'unknown';
        const userAgent = req?.get?.('User-Agent') || 'unknown';
        const timestamp = new Date().toISOString();

        console.log(`[SECURITY] ${timestamp} - Login attempt for: ${email} from IP: ${ipAddress}`);

        try {
            // Check if admin_user table exists and has users
            const adminUserExists = await this.checkAdminUserTable();

            if (adminUserExists) {
                // Use new admin_user table
                return await this.authenticateWithAdminUser(email, password, ipAddress, userAgent);
            } else {
                // Fallback to environment variables
                return await this.authenticateWithEnv(email, password, ipAddress, userAgent);
            }
        } catch (error) {
            console.error(`[SECURITY] ${timestamp} - Authentication error:`, error.message);
            return null;
        }
    }

    private async checkAdminUserTable(): Promise<boolean> {
        try {
            const result = await this.dataSource.query(`
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = 'admin_user'
                );
            `);

            if (result[0]?.exists) {
                // Check if table has any users
                const userCount = await this.dataSource.query('SELECT COUNT(*) as count FROM admin_user WHERE status = $1', ['active']);
                return userCount[0]?.count > 0;
            }

            return false;
        } catch (error) {
            console.log('[SECURITY] admin_user table not found, using fallback authentication');
            return false;
        }
    }

    private async authenticateWithAdminUser(email: string, password: string, ipAddress: string, userAgent: string): Promise<any> {
        try {
            // Get user from database
            const users = await this.dataSource.query(`
                SELECT id, name, email, password_hash, role, status,
                       failed_login_attempts, locked_until, last_login_at
                FROM admin_user
                WHERE email = $1 AND deleted_at IS NULL
            `, [email.toLowerCase()]);

            if (users.length === 0) {
                console.log(`[SECURITY] User not found: ${email}`);
                return null;
            }

            const user = users[0];

            // Check if account is locked
            if (user.locked_until && new Date(user.locked_until) > new Date()) {
                const lockTime = Math.ceil((new Date(user.locked_until).getTime() - Date.now()) / 1000 / 60);
                console.log(`[SECURITY] Account locked for ${email}, ${lockTime} minutes remaining`);
                return null;
            }

            // Check if account is active
            if (user.status !== 'active') {
                console.log(`[SECURITY] Account not active: ${email} (status: ${user.status})`);
                return null;
            }

            // Verify password
            const isPasswordValid = await bcrypt.compare(password, user.password_hash);

            if (!isPasswordValid) {
                // Increment failed attempts
                await this.handleFailedLogin(user.id, user.failed_login_attempts);
                console.log(`[SECURITY] Invalid password for: ${email}`);
                return null;
            }

            // Reset failed attempts and update last login
            await this.dataSource.query(`
                UPDATE admin_user
                SET failed_login_attempts = 0,
                    locked_until = NULL,
                    last_login_at = CURRENT_TIMESTAMP,
                    last_login_ip = $2
                WHERE id = $1
            `, [user.id, ipAddress]);

            console.log(`[SECURITY] Successful database login for: ${email} (role: ${user.role})`);

            return {
                email: user.email,
                role: user.role,
                id: user.id,
                name: user.name
            };

        } catch (error) {
            console.error('[SECURITY] Database authentication error:', error.message);
            return null;
        }
    }

    private async authenticateWithEnv(email: string, password: string, ipAddress: string, userAgent: string): Promise<any> {
        console.log('[SECURITY] Using fallback environment authentication');

        if (email === process.env.ADMIN_USERNAME && password === process.env.ADMIN_PASSWORD) {
            console.log(`[SECURITY] Successful fallback login for: ${email}`);
            return {
                email,
                role: 'super_admin',
                id: 'legacy-admin',
                name: 'Legacy Admin'
            };
        }

        console.log(`[SECURITY] Failed fallback login for: ${email}`);
        return null;
    }

    private async handleFailedLogin(userId: string, currentFailedAttempts: number): Promise<void> {
        const newFailedAttempts = currentFailedAttempts + 1;
        const maxAttempts = 5;
        const lockoutDuration = 15 * 60 * 1000; // 15 minutes

        let updateQuery = `
            UPDATE admin_user
            SET failed_login_attempts = $1
            WHERE id = $2
        `;
        let params = [newFailedAttempts, userId];

        if (newFailedAttempts >= maxAttempts) {
            const lockUntil = new Date(Date.now() + lockoutDuration);
            updateQuery = `
                UPDATE admin_user
                SET failed_login_attempts = $1, locked_until = $3
                WHERE id = $2
            `;
            params = [newFailedAttempts, userId, lockUntil.toISOString()];
            console.log(`[SECURITY] Account locked due to ${newFailedAttempts} failed attempts`);
        }

        await this.dataSource.query(updateQuery, params);
    }
}
