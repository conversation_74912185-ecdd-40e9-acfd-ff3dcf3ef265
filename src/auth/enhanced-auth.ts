import { AdminSecurityService } from '../admin-users/admin-security.service.js';
import { AdminUserService } from '../admin-users/admin-user.service.js';
import { Request } from 'express';
import { AuditActionEnum } from '../admin-users/admin-audit-log.entity.js';

interface AuthenticatedRequest extends Request {
    adminUser?: any;
    sessionToken?: string;
    session: any;
}

export function createEnhancedAuthentication(
    securityService: AdminSecurityService,
    userService: AdminUserService
) {
    return async (email: string, password: string, req?: AuthenticatedRequest) => {
        if (!req) {
            throw new Error('Request object is required for enhanced authentication');
        }

        const ipAddress = req.ip || req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
        const userAgent = req.get('User-Agent');
        const twoFactorToken = req.body?.twoFactorToken;

        try {
            const result = await securityService.authenticate({
                email,
                password,
                ipAddress,
                userAgent,
                twoFactorToken,
            });

            if (result.requiresTwoFactor) {
                // Store partial authentication state
                if (!req.session) {
                    req.session = {} as any;
                }
                (req.session as any).partialAuth = {
                    userId: result.user.id,
                    email: result.user.email,
                    timestamp: Date.now(),
                };

                return {
                    requiresTwoFactor: true,
                    user: {
                        email: result.user.email,
                        twoFactorEnabled: result.user.twoFactorEnabled,
                    },
                };
            }

            // Store session token in request for AdminJS
            req.sessionToken = result.sessionToken;
            req.adminUser = result.user;

            return {
                email: result.user.email,
                role: result.user.role,
                id: result.user.id,
                sessionToken: result.sessionToken,
            };
        } catch (error) {
            // Log failed authentication attempt
            await securityService.logAuditEvent({
                action: AuditActionEnum.LOGIN_FAILED,
                resource: 'AdminUser',
                ipAddress,
                userAgent,
                description: `Authentication failed: ${error.message}`,
                isSuccessful: false,
            });

            return null;
        }
    };
}

export function createSessionValidator(securityService: AdminSecurityService) {
    return async (req: AuthenticatedRequest, res: any, next: any) => {
        const sessionToken = req.headers.authorization?.replace('Bearer ', '') ||
                           req.cookies?.adminjs_session ||
                           req.sessionToken;

        if (!sessionToken) {
            return res.status(401).json({ error: 'No session token provided' });
        }

        const ipAddress = req.ip || req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';

        try {
            const user = await securityService.validateSession(sessionToken, ipAddress);

            if (!user) {
                return res.status(401).json({ error: 'Invalid or expired session' });
            }

            req.adminUser = user;
            req.sessionToken = sessionToken;
            next();
        } catch (error) {
            return res.status(401).json({ error: 'Session validation failed' });
        }
    };
}
