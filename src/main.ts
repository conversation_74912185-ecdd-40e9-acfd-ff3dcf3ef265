import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module.js';
import { logEnvironmentVariables } from './debug/env-check.js';
import { startMemoryMonitoring, optimizeForContainer } from './config/production.config.js';

import 'dotenv/config';

export async function bootstrap() {
    // Apply container optimizations for production
    optimizeForContainer();

    // Debug environment variables on startup
    logEnvironmentVariables();

    // Start memory monitoring in production
    startMemoryMonitoring();

    console.log('🏗️  Creating NestJS application...');
    const app = await NestFactory.create(AppModule, {
        logger: process.env.NODE_ENV === 'production' ? ['error', 'warn', 'log'] : ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    console.log(`🚀 Starting server on port ${process.env.PORT || 3000}`);
    await app.listen(process.env.PORT || 3000);

    console.log(`✅ Server is running on http://localhost:${process.env.PORT || 3000}`);
    console.log(`🔐 Admin panel: http://localhost:${process.env.PORT || 3000}/admin`);

    // Log initial memory usage
    const usage = process.memoryUsage();
    console.log(`📊 Initial memory usage: ${Math.round(usage.heapUsed / 1024 / 1024)}MB heap, ${Math.round(usage.rss / 1024 / 1024)}MB RSS`);
}
bootstrap();
