// Production Memory Optimization Configuration
export const productionConfig = {
    // Node.js memory optimization
    nodeOptions: {
        maxOldSpaceSize: 2048,
        optimizeForSize: true,
        maxSemiSpaceSize: 128,
    },
    
    // AdminJS bundling optimization
    adminJs: {
        bundleTimeout: 300000, // 5 minutes
        bundleCache: true,
        componentBundling: {
            minify: true,
            sourceMaps: false,
        },
    },
    
    // Session optimization (no Redis)
    session: {
        cleanupInterval: 300000, // 5 minutes
        maxAge: 7200000, // 2 hours
        pruneExpired: true,
        maxSessions: 1000, // Limit concurrent sessions
    },
    
    // Database connection optimization
    database: {
        connectionPoolSize: 5,
        connectionTimeout: 30000,
        idleTimeout: 300000,
        maxQueryExecutionTime: 30000,
    },
    
    // Memory monitoring
    monitoring: {
        logMemoryUsage: true,
        memoryLogInterval: 60000, // 1 minute
        gcForceInterval: 600000, // 10 minutes
    },
};

// Memory monitoring function
export function startMemoryMonitoring() {
    if (process.env.NODE_ENV !== 'production') {
        return;
    }
    
    console.log('[MEMORY] Starting memory monitoring for production');
    
    // Log memory usage periodically
    setInterval(() => {
        const usage = process.memoryUsage();
        const heapUsedMB = Math.round(usage.heapUsed / 1024 / 1024);
        const heapTotalMB = Math.round(usage.heapTotal / 1024 / 1024);
        const rssMB = Math.round(usage.rss / 1024 / 1024);
        
        console.log(`[MEMORY] Heap: ${heapUsedMB}/${heapTotalMB}MB, RSS: ${rssMB}MB`);
        
        // Warning if memory usage is high
        if (heapUsedMB > 1500) {
            console.warn(`[MEMORY] WARNING: High memory usage detected: ${heapUsedMB}MB`);
        }
        
        // Force garbage collection if memory is very high
        if (heapUsedMB > 1800 && global.gc) {
            console.log('[MEMORY] Forcing garbage collection');
            global.gc();
        }
    }, productionConfig.monitoring.memoryLogInterval);
    
    // Force garbage collection periodically
    if (global.gc) {
        setInterval(() => {
            console.log('[MEMORY] Periodic garbage collection');
            global.gc();
        }, productionConfig.monitoring.gcForceInterval);
    }
}

// ECS/Docker memory optimization
export function optimizeForContainer() {
    if (process.env.NODE_ENV !== 'production') {
        return;
    }
    
    console.log('[MEMORY] Applying container optimizations');
    
    // Set Node.js memory limits based on container memory
    const containerMemoryMB = parseInt(process.env.CONTAINER_MEMORY_MB || '2048');
    const nodeMemoryMB = Math.floor(containerMemoryMB * 0.8); // Use 80% of container memory
    
    process.env.NODE_OPTIONS = `${process.env.NODE_OPTIONS || ''} --max-old-space-size=${nodeMemoryMB}`;
    
    console.log(`[MEMORY] Container: ${containerMemoryMB}MB, Node.js limit: ${nodeMemoryMB}MB`);
    
    // Handle uncaught exceptions to prevent memory leaks
    process.on('uncaughtException', (error) => {
        console.error('[MEMORY] Uncaught exception:', error);
        // Log memory usage before exit
        const usage = process.memoryUsage();
        console.error('[MEMORY] Memory usage at crash:', {
            heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + 'MB',
            heapTotal: Math.round(usage.heapTotal / 1024 / 1024) + 'MB',
            rss: Math.round(usage.rss / 1024 / 1024) + 'MB',
        });
        process.exit(1);
    });
    
    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
        console.error('[MEMORY] Unhandled rejection at:', promise, 'reason:', reason);
    });
}
