import { SessionOptions, Store } from 'express-session';
import * as crypto from 'crypto';

export interface SessionConfig {
    sessionOptions: SessionOptions;
    store?: Store;
}

// Memory-optimized session store that cleans up expired sessions
class OptimizedMemoryStore extends Store {
    private sessions: Map<string, { data: any; expires: number }> = new Map();
    private cleanupInterval: NodeJS.Timeout;

    // Implement required Store interface methods
    public all(callback: (err?: any, obj?: any) => void) {
        const allSessions: any = {};
        for (const [sid, session] of this.sessions.entries()) {
            if (Date.now() <= session.expires) {
                allSessions[sid] = session.data;
            }
        }
        callback(null, allSessions);
    }

    constructor() {
        super();
        // Clean up expired sessions every 5 minutes
        const interval = parseInt(process.env.SESSION_CLEANUP_INTERVAL || '300000');
        this.cleanupInterval = setInterval(() => {
            this.cleanup();
        }, interval);

        console.log(`[SESSION] Memory store cleanup scheduled every ${interval}ms`);
    }

    get(sid: string, callback: (err?: any, session?: any) => void) {
        const session = this.sessions.get(sid);
        if (!session) {
            return callback();
        }

        if (Date.now() > session.expires) {
            this.sessions.delete(sid);
            return callback();
        }

        callback(null, session.data);
    }

    set(sid: string, session: any, callback?: (err?: any) => void) {
        const maxAge = parseInt(process.env.SESSION_MAX_AGE || '7200000'); // 2 hours default
        const expires = Date.now() + maxAge;

        this.sessions.set(sid, {
            data: session,
            expires: expires
        });

        if (callback) callback();
    }

    destroy(sid: string, callback?: (err?: any) => void) {
        this.sessions.delete(sid);
        if (callback) callback();
    }

    length(callback: (err: any, length?: number) => void) {
        callback(null, this.sessions.size);
    }

    clear(callback?: (err?: any) => void) {
        this.sessions.clear();
        if (callback) callback();
    }

    touch(sid: string, session: any, callback?: (err?: any) => void) {
        const existing = this.sessions.get(sid);
        if (existing) {
            const maxAge = parseInt(process.env.SESSION_MAX_AGE || '7200000');
            existing.expires = Date.now() + maxAge;
        }
        if (callback) callback();
    }

    private cleanup() {
        const now = Date.now();
        let cleaned = 0;

        for (const [sid, session] of this.sessions.entries()) {
            if (now > session.expires) {
                this.sessions.delete(sid);
                cleaned++;
            }
        }

        if (cleaned > 0) {
            console.log(`[SESSION] Cleaned up ${cleaned} expired sessions. Active sessions: ${this.sessions.size}`);
        }
    }

    getStats() {
        return {
            activeSessions: this.sessions.size,
            memoryUsage: process.memoryUsage()
        };
    }
}

export async function createSessionConfig(): Promise<SessionConfig> {
    const sessionSecret = process.env.SESSION_SECRET || crypto.randomBytes(32).toString('hex');

    console.log('[SESSION] Configuring optimized memory session store');

    // Use optimized memory store
    const store = new OptimizedMemoryStore();

    // Log memory usage periodically in production
    if (process.env.NODE_ENV === 'production') {
        setInterval(() => {
            const stats = store.getStats();
            const memMB = Math.round(stats.memoryUsage.heapUsed / 1024 / 1024);
            console.log(`[SESSION] Active sessions: ${stats.activeSessions}, Memory: ${memMB}MB`);
        }, 60000); // Every minute
    }

    const sessionOptions: SessionOptions = {
        secret: sessionSecret,
        resave: false,
        saveUninitialized: false,
        store: store,
        cookie: {
            secure: process.env.NODE_ENV === 'production' && process.env.HTTPS === 'true',
            httpOnly: true,
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
            sameSite: 'lax',
        },
        // Memory optimization
        rolling: true, // Reset expiration on activity
        name: 'adminjs.sid', // Custom session name
    };

    // Additional memory optimizations for production
    if (process.env.NODE_ENV === 'production') {
        sessionOptions.cookie!.secure = process.env.HTTPS === 'true';

        // Shorter session timeout in production if no Redis
        if (!store) {
            sessionOptions.cookie!.maxAge = 2 * 60 * 60 * 1000; // 2 hours instead of 24
            console.warn('[SESSION] Using shorter session timeout due to memory store');
        }
    }

    return {
        sessionOptions,
        store
    };
}
