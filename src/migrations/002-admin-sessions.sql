-- =====================================================
-- Admin Session Management Tables
-- =====================================================
-- Run this after creating the admin_user table
-- Provides secure session tracking and management
-- =====================================================

-- Create admin_session table
CREATE TABLE IF NOT EXISTS admin_session (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_user_id UUID NOT NULL REFERENCES admin_user(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_activity_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for session management
CREATE INDEX IF NOT EXISTS idx_admin_session_user ON admin_session(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_session_token ON admin_session(session_token);
CREATE INDEX IF NOT EXISTS idx_admin_session_expires ON admin_session(expires_at);
CREATE INDEX IF NOT EXISTS idx_admin_session_active ON admin_session(is_active);
