import {
    BaseEntity,
    Column,
    CreateDate<PERSON><PERSON>umn,
    DeleteDateColumn,
    Entity,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

@Entity('user_account')
export class User extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ nullable: true })
    name?: string;

    @Column({ nullable: true, unique: true })
    email?: string;

    @Column({ nullable: true, unique: true })
    username?: string;

    @Column({ nullable: true })
    password?: string;

    @Column({ type: 'text', nullable: true })
    profilePicture?: string;

    @Column({ type: 'text', nullable: true })
    description?: string;

    @Column({ type: 'text', nullable: true })
    website?: string;

    @Column({ nullable: true })
    timezone?: string;

    @Column({ nullable: true })
    frontendTheme?: string;

    @Column({ nullable: true })
    currency?: string;

    @Column({ nullable: true })
    locale?: string;

    @Column({ default: true })
    includeWatermarks: boolean;

    @Column({ default: true })
    isActive: boolean;

    @Column({ default: false })
    isVerified: boolean;

    @Column({ type: 'date', nullable: true })
    birthday?: Date;

    @Column({ type: 'timestamp', nullable: true })
    lastLogin?: Date;

    @Column({ type: 'timestamp', nullable: true })
    blockedAt?: Date;

    @Column({ type: 'timestamp', nullable: true })
    emailValidatedAt?: Date;

    @DeleteDateColumn({ type: 'timestamp', nullable: true })
    deletedAt?: Date;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @Column({ default: false })
    isBot: boolean;

    @Column({ nullable: true, default: 0 })
    followersCount?: number;

    @Column({ nullable: true, default: 0 })
    followingCount?: number;

    @Column({ nullable: true, default: 0 })
    imagesGenerated?: number;

    @Column({ nullable: true, default: 0 })
    imagesAvailable?: number;

    @Column({ nullable: true, default: 0 })
    modelsAvailable?: number;

    @Column({ type: 'json', nullable: true })
    tutorialSteps?: any;
}
