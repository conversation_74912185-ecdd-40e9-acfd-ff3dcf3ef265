// Environment Variable Debug Utility
export function logEnvironmentVariables() {
    console.log('\n🔍 Environment Variables Debug');
    console.log('==============================');
    
    const httpAuthVars = [
        'HTTP_AUTH_USERNAME',
        'HTTP_AUTH_PASSWORD', 
        'HTTP_AUTH_REALM',
        'HTTP_AUTH_ENABLED',
        'SKIP_HTTP_AUTH_DEV',
        'NODE_ENV'
    ];
    
    httpAuthVars.forEach(varName => {
        const value = process.env[varName];
        if (value) {
            // Mask password for security
            const displayValue = varName.includes('PASSWORD') ? '*'.repeat(value.length) : value;
            console.log(`✅ ${varName}=${displayValue}`);
        } else {
            console.log(`❌ ${varName}=<not set>`);
        }
    });
    
    console.log('\n🐳 Docker Environment Check:');
    console.log(`   Container: ${process.env.HOSTNAME || 'unknown'}`);
    console.log(`   Platform: ${process.platform}`);
    console.log(`   Node Version: ${process.version}`);
    console.log(`   Working Directory: ${process.cwd()}`);
    
    console.log('\n🔐 HTTP Auth Configuration:');
    const httpAuthEnabled = process.env.HTTP_AUTH_ENABLED !== 'false';
    const skipInDev = process.env.SKIP_HTTP_AUTH_DEV === 'true';
    const isDev = process.env.NODE_ENV === 'development';
    
    console.log(`   Enabled: ${httpAuthEnabled}`);
    console.log(`   Skip in Dev: ${skipInDev}`);
    console.log(`   Is Development: ${isDev}`);
    console.log(`   Will HTTP Auth Run: ${httpAuthEnabled && !(isDev && skipInDev)}`);
    
    console.log('==============================\n');
}
