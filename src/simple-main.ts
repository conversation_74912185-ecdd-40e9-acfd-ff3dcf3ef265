import { NestFactory } from '@nestjs/core';
import { SimpleAppModule } from './simple-app.module.js';
import 'dotenv/config';

export async function bootstrap() {
    console.log('🚀 Starting Letz.ai Admin Dashboard with Enhanced Security...');
    console.log('');
    
    const app = await NestFactory.create(SimpleAppModule);
    
    // Add basic security headers
    app.use((req, res, next) => {
        res.setHeader('X-Frame-Options', 'DENY');
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        res.removeHeader('X-Powered-By');
        next();
    });
    
    const port = process.env.PORT || 3013;
    await app.listen(port);
    
    console.log('');
    console.log(`🌐 Application running on: http://localhost:${port}`);
    console.log('🔒 Security enhancements active');
    console.log('');
    console.log('Next steps:');
    console.log('1. Switch to Node.js 18 for full AdminJS support');
    console.log('2. Generate secure secrets: npm run generate:secrets');
    console.log('3. Update your .env file with security settings');
    console.log('4. Enable full security features');
}

bootstrap();
