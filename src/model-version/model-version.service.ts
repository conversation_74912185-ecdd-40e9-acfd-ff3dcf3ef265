import { Injectable, Inject } from '@nestjs/common';
import { Repository } from 'typeorm';
import { ModelVersion } from './model-version.entity.js';

@Injectable()
export class ModelVersionService {
    constructor(
        @Inject('MODEL_VERSION_REPOSITORY')
        private modelVersionRepository: Repository<ModelVersion>,
    ) {}

    async findAll(): Promise<ModelVersion[]> {
        return this.modelVersionRepository.find();
    }
}
