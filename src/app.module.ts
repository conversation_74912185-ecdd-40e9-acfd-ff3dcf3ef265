import {
    Module,
    NestModule,
    MiddlewareConsumer,
    RequestMethod,
} from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller.js';
import { AppService } from './app.service.js';
import { UserModule } from './users/user.module.js';
import { ModelModule } from './model/model.module.js';
import { VideoModule } from './video/video.module.js';
import { ImageCompletionModule } from './image-completion/image-completion.module.js';
import { BoardModule } from './boards/boards.module.js';
import { UpscaleModule } from './upscale/upscale.module.js';
import { SignupCodeModule } from './user-signup-code/user-signup-code.module.js';
import { GlobalNotificationModule } from './global-notification/global-notification.module.js';
import { OrganizationModule } from './organizations/organization.module.js';
import { AdminUserModule } from './admin-users/admin-user.module.js';
import { AdminJSModule } from './admin/admin.module.js';
import { HttpBasicAuthMiddleware } from './auth/http-basic-auth.middleware.js';
import { HttpAuthService } from './auth/http-auth.service.js';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import * as fs from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import 'dotenv/config';

const __dirname = dirname(fileURLToPath(import.meta.url));

@Module({
    imports: [
        GlobalNotificationModule,
        UserModule,
        ModelModule,
        VideoModule,
        ImageCompletionModule,
        BoardModule,
        UpscaleModule,
        SignupCodeModule,
        OrganizationModule,
        AdminUserModule,
        AdminJSModule,

        // TypeORM Configuration
        TypeOrmModule.forRootAsync({
            useFactory: async () => {
                // SSL configuration for production
                let sslConfig = {};
                if (process.env.NODE_ENV === 'production') {
                    try {
                        const caCertPath =
                            '/usr/src/app/certs/eu-north-1-bundle.pem';
                        const caCert = fs.readFileSync(caCertPath).toString();
                        sslConfig = {
                            ssl: {
                                rejectUnauthorized: false,
                                ca: caCert,
                            },
                        };
                        console.log(
                            '[DATABASE] SSL configuration loaded for production',
                        );
                    } catch (sslError) {
                        console.warn(
                            '[DATABASE] SSL cert not found, using non-SSL connection:',
                            sslError.message,
                        );
                    }
                }

                return {
                    type: 'postgres',
                    host: process.env.POSTGRES_HOST || 'localhost',
                    port: parseInt(process.env.POSTGRES_PORT || '5432'),
                    username: process.env.POSTGRES_USER || 'letzai',
                    password: process.env.POSTGRES_PASSWORD || 'letzai',
                    database: process.env.POSTGRES_DB || 'letzai',
                    entities: [join(__dirname, '**', '*.entity.{ts,js}')],
                    synchronize: false,
                    logging: false,
                    namingStrategy: new SnakeNamingStrategy(),
                    ...sslConfig,
                };
            },
        }),
    ],
    controllers: [AppController],
    providers: [AppService, HttpAuthService],
})
export class AppModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        // Apply HTTP Basic Auth middleware to all admin routes
        console.log(
            '[HTTP_AUTH] Configuring HTTP Basic Auth middleware for admin routes',
        );

        consumer
            .apply(HttpBasicAuthMiddleware)
            .forRoutes('/admin', '/admin/*', {
                path: '/admin*',
                method: RequestMethod.ALL,
            });

        console.log('[HTTP_AUTH] HTTP Basic Auth middleware configured');
    }
}
