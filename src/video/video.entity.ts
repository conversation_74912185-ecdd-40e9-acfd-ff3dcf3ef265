import { User } from '../users/user.entity.js';
import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

export enum StatusEnum {
    NEW = 'new',
    GENERATING = 'generating',
    READY = 'ready',
    SAVED = 'saved',
    FAILED = 'failed',
    INTERRUPTED = 'interrupted',
}

export enum PrivacyEnum {
    PUBLIC = 'public',
    PRIVATE = 'private',
    LICENSED = 'licensed',
}

@Entity('video')
export class Video extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ type: 'uuid', nullable: false })
    @Index('idx_video_user_id')
    userId: string;

    @ManyToOne(() => User)
    user: User;

    @Column({ type: 'text', nullable: true })
    prompt?: string;

    @Column({ type: 'text', nullable: true })
    promptSystem?: string;

    @Column({ type: 'text', nullable: true })
    storageBucket?: string;

    @Column({ type: 'text', nullable: true })
    storagePath?: string;

    @Column({ nullable: false, default: 480 })
    resolution?: number;

    @Column({ nullable: false, default: 480 })
    width: number;

    @Column({ nullable: false, default: 480 })
    height: number;

    @Column({ type: 'json', nullable: true })
    settings?: any;

    @Column({ type: 'json', nullable: true, default: {} })
    generationSettings?: any;

    @Column({ type: 'text', nullable: true })
    webhookUrl?: string;

    @Column({ type: 'text', nullable: true })
    inputImageUrl?: string;

    @Column({ nullable: false, default: StatusEnum.NEW })
    status: string;

    @Column({ nullable: false, default: 0 })
    progress: number;

    @Column({ nullable: false, default: 1 })
    systemVersion?: number;

    @Column({ nullable: false, default: 0 })
    generationSeconds: number;

    @Column({ type: 'json', nullable: true })
    videoPaths?: any;

    @Column({ type: 'int', nullable: false, default: 0 })
    imageCompletionsCount: number;

    @Column({ nullable: false, default: 0 })
    comments: number;

    @Column({ nullable: false, default: 0 })
    likes: number;

    @Column({ nullable: false, default: 0 })
    regenerations: number;

    @Column({ nullable: false, default: 0 })
    reports: number;

    @Column({
        type: 'enum',
        enum: PrivacyEnum,
        default: PrivacyEnum.PUBLIC,
        nullable: false,
    })
    privacy: PrivacyEnum;

    @Column({ default: false })
    isNsfw: boolean;

    @Column({ default: false })
    hidePrompt: boolean;

    @Column({ default: false })
    hideFromUserProfile: boolean;

    @Column({ default: false })
    isUnsafe: boolean;

    @Column({ default: false })
    @Index()
    isHot: boolean;

    @Column({ default: false })
    isActive: boolean;

    @Column({ type: 'timestamp', nullable: true })
    publishedAt?: Date;

    @Column({ type: 'timestamp', nullable: true })
    blockedAt?: Date;

    @DeleteDateColumn({ type: 'timestamp', nullable: true })
    @Index('idx_video_deleted_at')
    deletedAt?: Date;

    @CreateDateColumn()
    @Index('idx_video_created_at')
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
