import { Inject, Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { GlobalNotification } from './global-notification.entity.js';

@Injectable()
export class GlobalNotificationService {
    constructor(
        @Inject('GLOBAL_NOTIFICATION_REPOSITORY')
        private globalNotificationRepository: Repository<GlobalNotification>,
    ) {}

    async findAll(): Promise<GlobalNotification[]> {
        return this.globalNotificationRepository.find();
    }
}
