import { Module } from '@nestjs/common';
import { DatabaseModule } from '../database.module.js';
import { globalNotificationProviders } from './global-notification.provider.js';
import { GlobalNotificationService } from './global-notification.service.js';

@Module({
    imports: [DatabaseModule],
    providers: [...globalNotificationProviders, GlobalNotificationService],
})
export class GlobalNotificationModule {}
