import { En<PERSON>ty, PrimaryColumn, Column, BaseEntity } from 'typeorm';

@Entity('global_notification')
export class GlobalNotification extends BaseEntity {
    @PrimaryColumn('varchar', { default: 'global' })
    id = 'global';

    @Column()
    title: string;

    @Column()
    message: string;

    @Column()
    textColor: string;

    @Column()
    backgroundColor: string;

    @Column()
    isActive: boolean;
}
