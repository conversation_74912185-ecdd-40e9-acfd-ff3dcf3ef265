import {
  BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
  } from 'typeorm';
  import { User } from '../users/user.entity.js';

  export enum VisibilityEnum {
    PUBLIC = 'public',
    PRIVATE = 'private',
    HIDDEN = 'hidden',
  }
  

  export enum SafetyEnum {
    NSFW = 'nsfw',
    SFW = 'sfw',
  }  
  
  @Entity('board')
  export class Board extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;
  
    @Column({ nullable: false })
    name: string;
  
    @ManyToOne(() => User, { eager: true })
    owner: User;
  
    @Column({ type: 'uuid', nullable: true })
    @Index()
    ownerId: string;
  
    @Column({ type: 'text', nullable: true })
    description?: string;
  
    @Column({ type: 'text', nullable: true })
    thumbnail?: string;
  
    @Column({
      type: 'enum',
      enum: VisibilityEnum,
      default: VisibilityEnum.PUBLIC,
      nullable: false,
    })
    visibility: VisibilityEnum;
  
    @Column({
      type: 'enum',
      enum: SafetyEnum,
      default: SafetyEnum.SFW,
      nullable: false,
    })
    safety: SafetyEnum;
  
    @Column({ type: 'timestamp', nullable: true })
    challengeDeadline: Date;
  
    @Column({ default: false })
    isDefault: boolean;
  
    @Column({ default: true })
    isActive: boolean;
  
    @Column({ nullable: false, default: 1 })
    members: number;
  
    @DeleteDateColumn({ type: 'timestamp', nullable: true })
    deletedAt?: Date;
  
    @CreateDateColumn()
    createdAt: Date;
  
    @UpdateDateColumn()
    updatedAt: Date;
  }
  