import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

@Entity('organization')
export class Organization extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ type: 'text', nullable: true })
    description?: string;

    @Column({ type: 'text', nullable: true })
    website?: string;

    @Column({ nullable: true, unique: true })
    @Index('idx_organization_handle')
    handle?: string;

    @Column({ type: 'text', nullable: true })
    profilePicture?: string;

    @Column({ nullable: false, default: 0 })
    imagesGenerated: number;

    @Column({ nullable: false, default: 0 })
    imagesAvailable: number;

    @Column({ nullable: false, default: 0 })
    modelsAvailable: number;

    @Column({ default: true })
    includeWatermarks: boolean;

    @Column({ nullable: false, default: 1 })
    members: number;

    @Column({ nullable: false, default: 1 })
    seatsPurchased: number;

    @Column({ default: true })
    isActive: boolean;

    @DeleteDateColumn({ type: 'timestamp', nullable: true })
    deletedAt?: Date;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
