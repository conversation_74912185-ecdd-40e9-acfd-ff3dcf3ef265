import { Inject, Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { Organization } from './organization.entity.js';

@Injectable()
export class OrganizationService {
    constructor(
        @Inject('ORGANIZATION_REPOSITORY')
        private organizationRepository: Repository<Organization>,
    ) {}

    async findAll(): Promise<Organization[]> {
        return this.organizationRepository.find();
    }
}
