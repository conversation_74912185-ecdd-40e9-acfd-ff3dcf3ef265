import React, { useState, useEffect } from 'react';
const YearlyStatisticsForm = ({ year, setYear, handleYearlyStatistics }) => {
    const [inputSearch, setInputSearch] = useState(year.toString());
    const handleSubmit = (event) => {
        event.preventDefault();
        setYear(Number(inputSearch));
        handleYearlyStatistics(inputSearch);
    };

    return (
        <form
            onSubmit={handleSubmit}
            style={{
                margin: '24px',
                marginBottom: '64px',
                display: 'flex',
                gap: '10px',
                alignItems: 'center',
                fontSize: '16px',
            }}
        >
            <span>Year: </span>
            <input
                type="number"
                name=""
                id=""
                defaultValue={year}
                onChange={(e) => setInputSearch(e.target.value)}
                style={{
                    padding: '10px',
                    border: '1px solid gray',
                    borderRadius: '5px',
                    fontSize: '16px',
                    maxWidth: '60px',
                }}
            />
            <button
                style={{
                    padding: '10px',
                    fontSize: '16px',
                    borderRadius: '5px',
                    border: '1px solid gray',
                    cursor: 'pointer',
                }}
            >
                Search Statistics
            </button>
        </form>
    );
};

export default YearlyStatisticsForm;
