import React, { useState } from 'react';
import { Box, Button, Input, FormGroup, Label, Text } from '@adminjs/design-system';
import { ApiClient, useNotice } from 'adminjs';

// Component for changing admin user passwords

const ChangePasswordForm = (props) => {
    const { record, resource, action } = props;
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const addNotice = useNotice();

    const handleSubmit = async (event) => {
        event.preventDefault();
        setLoading(true);

        try {
            // Basic validation
            if (!newPassword || !confirmPassword) {
                addNotice({
                    message: 'Both password fields are required',
                    type: 'error'
                });
                setLoading(false);
                return;
            }

            if (newPassword !== confirmPassword) {
                addNotice({
                    message: 'Passwords do not match',
                    type: 'error'
                });
                setLoading(false);
                return;
            }

            if (newPassword.length < 12) {
                addNotice({
                    message: 'Password must be at least 12 characters long',
                    type: 'error'
                });
                setLoading(false);
                return;
            }

            // Submit to the action handler
            const api = new ApiClient();
            const response = await api.recordAction({
                resourceId: resource.id,
                recordId: record.id,
                actionName: 'changePassword',
                data: {
                    newPassword,
                    confirmPassword
                }
            });

            if (response.data.notice && response.data.notice.type === 'success') {
                addNotice({
                    message: response.data.notice.message,
                    type: 'success'
                });
                
                // Reset form
                setNewPassword('');
                setConfirmPassword('');
                
                // Redirect back to the resource list after a short delay
                setTimeout(() => {
                    window.location.href = `/admin/resources/${resource.id}`;
                }, 1500);
            } else if (response.data.notice && response.data.notice.type === 'error') {
                addNotice({
                    message: response.data.notice.message,
                    type: 'error'
                });
            }
        } catch (error) {
            console.error('Password change error:', error);
            addNotice({
                message: 'An error occurred while changing the password',
                type: 'error'
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <Box>
            <Box mb="xl">
                <Text variant="lg" fontWeight="bold">
                    Change Password for: {record.params.name || 'User'}
                </Text>
            </Box>

            <form onSubmit={handleSubmit}>
                <FormGroup>
                    <Label required>New Password</Label>
                    <Input
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        placeholder="Enter new password (min 12 characters)"
                        required
                        minLength="12"
                    />
                    <Text variant="sm" color="grey60" mt="sm">
                        Password must be at least 12 characters with uppercase, lowercase, numbers, and special characters.
                    </Text>
                </FormGroup>

                <FormGroup mt="lg">
                    <Label required>Confirm Password</Label>
                    <Input
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        placeholder="Confirm new password"
                        required
                    />
                </FormGroup>

                <Box mt="xl" display="flex" gap="sm">
                    <Button 
                        type="submit" 
                        variant="primary" 
                        disabled={loading}
                    >
                        {loading ? 'Changing Password...' : 'Change Password'}
                    </Button>
                    <Button 
                        type="button" 
                        variant="secondary" 
                        onClick={() => window.history.back()}
                    >
                        Cancel
                    </Button>
                </Box>
            </form>
        </Box>
    );
};

export default ChangePasswordForm;
