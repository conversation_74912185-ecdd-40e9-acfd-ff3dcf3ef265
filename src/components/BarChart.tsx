import React from 'react';
import { Bar, Line } from 'react-chartjs-2';
// import { Chart as ChartJS } from "chart.js/auto";
import {
    Chart as ChartJS,
    ArcElement,
    LineElement,
    BarElement,
    PointElement,
    BarController,
    BubbleController,
    DoughnutController,
    LineController,
    PieController,
    PolarAreaController,
    RadarController,
    ScatterController,
    CategoryScale,
    LinearScale,
    LogarithmicScale,
    RadialLinearScale,
    TimeScale,
    TimeSeriesScale,
    Decimation,
    Filler,
    Legend,
    Title,
    Tooltip,
} from 'chart.js';

ChartJS.register(
    ArcElement,
    LineElement,
    BarElement,
    PointElement,
    BarController,
    BubbleController,
    DoughnutController,
    LineController,
    PieController,
    PolarAreaController,
    RadarController,
    ScatterController,
    CategoryScale,
    LinearScale,
    LogarithmicScale,
    RadialLinearScale,
    TimeScale,
    TimeSeriesScale,
    Decimation,
    Filler,
    Legend,
    Title,
    Tooltip,
);

function BarChart({ labels, label, colors, statistics }) {
    const chartData = {
        labels: labels,
        datasets: [
            {
                label: label,
                data: statistics,
                backgroundColor: colors,
                borderColor: 'black',
                borderWidth: 1,
            },
        ],
    };
    return <Line data={chartData} />;
}

export default BarChart;
