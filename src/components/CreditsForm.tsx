import React, { useState } from 'react';

const CreditsForm = ({ handleAddCredits, creditType }) => {
    const today = new Date();
    today.setFullYear(today.getFullYear()+1);
    const [formData, setFormData] = useState({
        amount: '',
        expiresAt: today.toISOString().split('T')[0],
    });

    const handleSubmit = (event) => {
        event.preventDefault();
        const body = { ...formData, creditType };
        handleAddCredits(body);
    };

    return (
        <form
            style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '10px',
                textTransform: 'capitalize',
                width: '250px',
                justifyContent: 'center',
                padding: '15px',
                border: '1px solid black',
                borderRadius: '15px',
            }}
            onSubmit={handleSubmit}
        >
            <h2
                style={{
                    fontSize: '20px',
                    textAlign: 'center',
                }}
            >
                {creditType}
            </h2>
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                }}
            >
                <span
                    style={{
                        fontSize: '12px',
                        color: 'gray',
                    }}
                >
                    Amount of credits
                </span>
                <input
                    type="number"
                    style={{
                        padding: '10px',
                        borderRadius: '15px',
                        borderWidth: '1px',
                    }}
                    onChange={(e) =>
                        setFormData({
                            ...formData,
                            amount: e.target.value,
                        })
                    }
                />
            </div>
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                }}
            >
                <span
                    style={{
                        fontSize: '12px',
                        color: 'gray',
                    }}
                >
                    Expires at
                </span>
                <input
                    type="date"
                    style={{
                        padding: '10px',
                        borderRadius: '15px',
                        borderWidth: '1px',
                    }}
                    onChange={(e) =>
                        setFormData({
                            ...formData,
                            expiresAt: e.target.value,
                        })
                    }
                    defaultValue={today.toISOString().split('T')[0]}
                    min={today.toISOString().split('T')[0]}
                />
            </div>
            <button
                style={{
                    padding: '10px',
                    fontSize: '16px',
                    borderRadius: '10px',
                    border: '1px solid gray',
                    cursor: 'pointer',
                }}
            >
                Add Credits
            </button>
        </form>
    );
};

export default CreditsForm;
