import { Show } from 'adminjs';
import React, { useState, useEffect } from 'react';
import axios from 'axios';

const VideoDetail = (props) => {
    const [videoData, setVideoData] = useState(null);

    useEffect(() => {
        const loadVideoData = async () => {
            try {
                const { data } = await axios.get(
                    process.env.API_URL + `/internal/videos/${props.record.id}`,
                    {
                        headers: {
                            'x-api-key': process.env.API_KEY,
                        },
                    },
                );
                setVideoData(data);
            } catch (error) {
                console.error('Error loading video data:', error);
            }
        };
        loadVideoData();
    }, []);

    const convertDate = (oldDate) => {
        if (oldDate == null) {
            return 'Date not specified';
        }
        const convertedDate = new Date(oldDate).toLocaleString();
        return convertedDate;
    };

    return (
        <>
            <Show {...props} />
            <div
                style={{
                    padding: '10px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '20px',
                    marginTop: '20px',
                }}
            >
                <h2
                    style={{
                        fontSize: '32px',
                    }}
                >
                    Video Details
                </h2>
                <div
                    style={{
                        backgroundColor: 'white',
                        padding: '20px',
                        gap: '10px',
                        display: 'flex',
                        flexDirection: 'column',
                        border: '1px solid #cbcac7',
                        borderRadius: '25px',
                        width: '100%',
                    }}
                >
                    <div
                        style={{
                            display: 'flex',
                            gap: '5px',
                            border: '1px solid #ff6e50',
                            color: '#ff6e50',
                            padding: '5px',
                            borderRadius: '10px',
                            maxWidth: '300px',
                            fontSize: '12px',
                            marginBottom: '20px',
                            justifyContent: 'center',
                        }}
                    >
                        <p>Video ID: </p>
                        <span>{props.record.id}</span>
                    </div>
                    <div
                        style={{
                            display: 'flex',
                            gap: '20px',
                            justifyContent: 'space-between',
                            flexWrap: 'wrap',
                        }}
                    >
                        <div
                            style={{
                                display: 'flex',
                                gap: '5px',
                                border: '1px solid #000000',
                                padding: '10px',
                                borderRadius: '10px',
                                width: '260px',
                                justifyContent: 'center',
                            }}
                        >
                            <p>Created At: </p>
                            <span>
                                {convertDate(props.record.params.createdAt)}
                            </span>
                        </div>
                        <div
                            style={{
                                display: 'flex',
                                gap: '5px',
                                border: '1px solid #507aff',
                                color: '#507aff',
                                padding: '10px',
                                borderRadius: '10px',
                                width: '260px',
                                justifyContent: 'center',
                            }}
                        >
                            <p>Updated At: </p>
                            <span>
                                {convertDate(props.record.params.updatedAt)}
                            </span>
                        </div>
                    </div>
                    {videoData && (
                        <div
                            style={{
                                marginTop: '20px',
                                padding: '15px',
                                backgroundColor: '#f8f9fa',
                                borderRadius: '10px',
                            }}
                        >
                            <p style={{ color: '#666', fontSize: '14px' }}>
                                Additional video data will be displayed here when available.
                            </p>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

export default VideoDetail;
