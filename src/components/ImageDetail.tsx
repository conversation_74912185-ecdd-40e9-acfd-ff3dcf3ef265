import React, { useState, useEffect } from 'react';
import { Show } from 'adminjs';
import axios from 'axios';

//import { ApiClient } from "adminjs";

const ImageDetail = (props) => {
    const [image, setImage] = useState('');

    useEffect(() => {
        const loadImage = async () => {
            const { data } = await axios.get(
                process.env.API_URL +
                    `/internal/image_completions/${props.record.id}`,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setImage(data.imageVersions['1920x1920']);
        };
        loadImage();
    }, []);

    /*useEffect(() => {
        async function fetchData() {
            const api = new ApiClient();
            api.getDashboard({
                params: {
                    id: id,
                },
            })
                .then((response: any) => {
                    console.log(response);
                })
                .catch((error) => {
                    console.error("Error fetching data:", error);
                });
        }

        fetchData();
    }, [id]);*/

    return (
        <>
            <div
                style={{
                    display: 'flex',
                }}
            >
                <img
                    src={image}
                    style={{
                        maxWidth: '500px',
                        maxHeight: '300px',
                        margin: '20px auto',
                        border: '4px solid transparent',
                    }}
                    loading="lazy"
                />
            </div>
            <Show {...props} />
        </>
    );
};

export default ImageDetail;
// function useEffect(arg0: () => void, arg1: any[]) {
//     throw new Error("Function not implemented.");
// }
