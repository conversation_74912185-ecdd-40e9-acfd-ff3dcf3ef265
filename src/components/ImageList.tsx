import { Box, Button, CheckBox, Label, Modal } from '@adminjs/design-system';
import axios from 'axios';
import React, { useEffect, useState } from 'react';

import { ApiClient } from 'adminjs';
import NewForm from './NewForm.js';

type ImageListProps = {
    apiUrl: string;
    apiKey: string;
};

const ImageList: React.FC<ImageListProps> = ({ apiUrl, apiKey }) => {
    const [images, setImages] = useState(null);
    const [page, setPage] = useState(1);
    const [totalImagePages, setTotalImagePages] = useState(1);
    const [totalModelPages, setTotalModelPages] = useState(1);

    const [showModal, setShowModal] = useState(false);
    const [models, setModels] = useState([]);
    const [selectedModels, setSelectedModels] = useState([]);
    const [currentSelectedModels, setCurrentSelectedModels] = useState([]);
    const [modelPage, setModelPage] = useState(1);

    const [loading, setLoading] = useState(false);
    const [isHot, setIsHot] = useState(false);
    const [includeNsfw, setIncludeNsfw] = useState(true);
    const [showPublic, setShowPublic] = useState(false);

    const api = new ApiClient();

    useEffect(() => {
        async function fetchData() {
            const params = {
                page: page,
                modelPage: modelPage,
                modelList: currentSelectedModels,
                isHot: isHot,
                includeNsfw: includeNsfw,
                privacy: showPublic ? 'public' : undefined,
            };

            api.getDashboard({
                params,
            })
                .then((response: any) => {
                    setImages(response?.data?.images);
                    setModels(response?.data?.models);
                    setTotalImagePages(response?.data?.totalImagePages);
                    setTotalModelPages(response?.data?.totalModelPages);
                    console.log(images);
                })
                .catch((error) => {
                    console.error('Error fetching data:', error);
                });

            setLoading(false);
        }

        fetchData();
    }, [
        page,
        modelPage,
        currentSelectedModels,
        isHot,
        showPublic,
        includeNsfw,
    ]);

    const handleModelSelection = (modelId) => {
        setSelectedModels((prev) => {
            if (prev.includes(modelId)) {
                return prev.filter((id) => id !== modelId);
            } else {
                return [...prev, modelId];
            }
        });
    };

    const handleFilterModel = async (value) => {
        if (value == '') {
            const { data } = await axios.get(
                process.env.API_URL +
                    `/internal/models?page=1&sortBy=name&limit=35`,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setModels(data);
        } else {
            const { data } = await axios.get(
                process.env.API_URL +
                    `/internal/models?page=1&sortBy=name&limit=35&search=${value}`,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setModels(data);
        }
    };

    return (
        <Box style={{ padding: '10px' }}>
            <Box>
                <Button variant="info" onClick={() => setShowModal(true)}>
                    Filter Models
                </Button>

                {showModal && (
                    <Modal
                        onClose={() => setShowModal(false)}
                        label="Filter by Model Name"
                    >
                        <NewForm handleFilterModel={handleFilterModel} />
                        <div
                            style={{
                                maxHeight: '300px',
                                overflowY: 'auto',
                                width: '100%',
                                border: '1px solid #ddd',
                                padding: '10px',
                            }}
                        >
                            {models.map((model) => (
                                <div
                                    key={model.id}
                                    style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '5px',
                                        padding: '5px 0',
                                    }}
                                >
                                    <input
                                        type="checkbox"
                                        checked={selectedModels.includes(
                                            model.id,
                                        )}
                                        onChange={(e) => {
                                            handleModelSelection(model.id);
                                        }}
                                    />
                                    {model.name} ({model.usages})
                                </div>
                            ))}
                        </div>
                        <Box
                            display="flex"
                            justifyContent="space-between"
                            style={{ marginTop: '2rem' }}
                        >
                            <Box>
                                <Button
                                    variant="light"
                                    color="primary"
                                    onClick={() =>
                                        setModelPage((prev) => prev - 1)
                                    }
                                    disabled={modelPage <= 1}
                                >
                                    Previous
                                </Button>
                                <Button
                                    variant="light"
                                    color="primary"
                                    onClick={() =>
                                        setModelPage((prev) => prev + 1)
                                    }
                                    disabled={modelPage >= totalModelPages}
                                >
                                    Next
                                </Button>
                            </Box>
                            <Button
                                onClick={() => {
                                    setCurrentSelectedModels(selectedModels);
                                    setShowModal(false);
                                }}
                            >
                                Apply
                            </Button>
                        </Box>
                    </Modal>
                )}

                <Button
                    variant="light"
                    color="primary"
                    onClick={() => setPage((prev) => prev - 1)}
                    disabled={page <= 1}
                >
                    Previous
                </Button>
                <Button
                    variant="light"
                    color="primary"
                    onClick={() => setPage((prev) => prev + 1)}
                    disabled={page >= totalImagePages}
                >
                    Next
                </Button>
                <CheckBox
                    onChange={() => setIsHot((prev) => !prev)}
                    checked={isHot}
                    style={{ marginLeft: '1rem' }}
                />
                <Label htmlFor="isHot" inline>
                    Show hot
                </Label>
                <CheckBox
                    onChange={() => setShowPublic((prev) => !prev)}
                    checked={showPublic}
                    style={{ marginLeft: '1rem' }}
                />
                <Label htmlFor="showPublic" inline>
                    Show Only Public
                </Label>
                <CheckBox
                    onChange={() => setIncludeNsfw((prev) => !prev)}
                    checked={includeNsfw}
                    style={{ marginLeft: '1rem' }}
                />
                <Label htmlFor="includeNsfw" inline>
                    Include NSFW
                </Label>
            </Box>
            {loading ? (
                <div>Loading ...</div>
            ) : (
                <Box flex flexDirection="row" flexWrap="wrap">
                    {images && images.length > 0
                        ? images?.map(
                              (imageCompletion: any) =>
                                  imageCompletion.imageVersions && (
                                      <div
                                          style={{
                                              width: '300px',
                                              height: '300px',
                                              backgroundColor: '#f0f0f0',
                                          }}
                                          key={imageCompletion.id}
                                      >
                                          <a
                                              href={`/admin/resources/ImageCompletion/records/${imageCompletion.id}/show`}
                                              style={{ cursor: 'pointer' }}
                                          >
                                              <img
                                                  src={
                                                      imageCompletion
                                                          .imageVersions[
                                                          '640x640'
                                                      ]
                                                  }
                                                  alt={imageCompletion.prompt}
                                                  key={
                                                      imageCompletion.storagePath
                                                  }
                                                  style={{
                                                      maxWidth: '300px',
                                                      margin: '5px',
                                                  }}
                                                  loading="lazy"
                                              />
                                          </a>
                                      </div>
                                  ),
                          )
                        : 'No images found'}
                </Box>
            )}
            <Box
                style={{
                    marginTop: '1rem',
                }}
            >
                <Button
                    variant="light"
                    color="primary"
                    onClick={() => setPage((prev) => prev - 1)}
                    disabled={page <= 1}
                >
                    Previous
                </Button>
                <Button
                    variant="light"
                    color="primary"
                    onClick={() => setPage((prev) => prev + 1)}
                    disabled={page >= totalImagePages}
                >
                    Next
                </Button>
            </Box>
        </Box>
    );
};

export default ImageList;
