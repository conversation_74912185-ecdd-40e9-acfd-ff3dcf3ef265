import React, { useState, useEffect } from 'react';
const MonthlyStatisticsForm = ({
    year,
    setYear,
    month,
    setMonth,
    handleMonthlyStatistics,
}) => {
    const [inputSearch, setInputSearch] = useState(year.toString());
    const [inputSearch2, setInputSearch2] = useState((month+1).toString());
    const handleSubmit = (event) => {
        event.preventDefault();
        setYear(Number(inputSearch));
        setMonth(Number(inputSearch2) - 1);
        handleMonthlyStatistics(inputSearch, inputSearch2);
    };

    return (
        <form
            onSubmit={handleSubmit}
            style={{
                margin: '24px',
                marginBottom: '64px',
                display: 'flex',
                gap: '10px',
                alignItems: 'center',
                fontSize: '16px',
            }}
        >
            <span>Month: </span>
            <input
                type="number"
                name=""
                id=""
                defaultValue={month + 1}
                min="1"
                max="12"
                onChange={(e) => setInputSearch2(e.target.value)}
                style={{
                    padding: '10px',
                    border: '1px solid gray',
                    borderRadius: '5px',
                    fontSize: '16px',
                    maxWidth: '60px',
                }}
            />
            <span>of the Year: </span>
            <input
                type="number"
                name=""
                id=""
                defaultValue={year}
                onChange={(e) => setInputSearch(e.target.value)}
                style={{
                    padding: '10px',
                    border: '1px solid gray',
                    borderRadius: '5px',
                    fontSize: '16px',
                    maxWidth: '60px',
                }}
            />
            <button
                style={{
                    padding: '10px',
                    fontSize: '16px',
                    borderRadius: '5px',
                    border: '1px solid gray',
                    cursor: 'pointer',
                }}
            >
                Search Statistics
            </button>
        </form>
    );
};

export default MonthlyStatisticsForm;
