import React, { useState, useEffect } from 'react';
const NewForm = ({ handleFilterModel }) => {
    const [inputSearch, setInputSearch] = useState('');
    const handleSubmit = (event) => {
        event.preventDefault();
        handleFilterModel(inputSearch);
    };
    return (
        <form
            onSubmit={handleSubmit}
            style={{
                marginBottom: '10px',
                marginTop: '10px',
                display: 'flex',
                gap: '5px',
            }}
        >
            <input
                type="text"
                placeholder="Model Name"
                onChange={(e) => setInputSearch(e.target.value)}
                style={{
                    padding: '10px',
                    border: '1px solid #ddd',
                    borderRadius: '6px',
                }}
            />
            <button
                type="submit"
                style={{
                    padding: '10px',
                    color: 'blue',
                    backgroundColor: 'white',
                    border: '1px solid #ddd',
                    borderRadius: '6px',
                    cursor: 'pointer',
                }}
            >
                Search
            </button>
        </form>
    );
};

export default NewForm;
