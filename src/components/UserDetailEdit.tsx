import React, { useState, useEffect } from 'react';
import { Edit } from 'adminjs';
import axios from 'axios';
import CreditsForm from './CreditsForm.js';

const UserDetailEdit = (props) => {
    const [credits, setCredits] = useState([]);
    const [images, setImages] = useState([]);

    const handleAddCredits = async (value) => {
        const loadImage = async () => {
            const body = {
                username: props.record.params.username,
                amount: '0',
                expiresAt: new Date().toISOString().split('T')[0],
                creditType: 'image',
            };
            const { data } = await axios.post(
                process.env.API_URL + `/internal/user-credit-balance`,
                body,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setCredits(data);
        };

        const loadModel = async () => {
            const body = {
                username: props.record.params.username,
                amount: '0',
                expiresAt: new Date().toISOString().split('T')[0],
                creditType: 'model',
            };
            const { data } = await axios.post(
                process.env.API_URL + `/internal/user-credit-balance`,
                body,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setCredits(data);
        };

        const body = {
            username: props.record.params.username,
            ...value,
        };
        const { data } = await axios.post(
            process.env.API_URL + `/internal/user-credit-balance`,
            body,
            {
                headers: {
                    'x-api-key': process.env.API_KEY,
                },
            },
        );
        setCredits(data);

        if (value.creditType == 'model') {
            loadImage();
        } else {
            loadModel();
        }
    };

    useEffect(() => {
        const loadCredits = async () => {
            const body = {
                username: props.record.params.username,
                amount: '0',
                expiresAt: new Date().toISOString().split('T')[0],
                creditType: 'image',
            };
            const { data } = await axios.post(
                process.env.API_URL + `/internal/user-credit-balance`,
                body,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setCredits(data);
        };
        loadCredits();
    }, []);

    useEffect(() => {
        const loadImages = async () => {
            const { data } = await axios.get(
                process.env.API_URL +
                    `/internal/image_completions?sortBy=createdAt&sortOrder=DESC&page=1&limit=10&userId=${props.record.id}&status=ready`,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setImages(data);
        };
        loadImages();
    }, []);

    return (
        <>
            <Edit {...props} />
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '20px',
                    padding: '10px',
                }}
            >
                <h2
                    style={{
                        fontSize: '32px',
                        marginTop: '20px',
                    }}
                >
                    User Credits
                </h2>
                <div
                    style={{
                        display: 'flex',
                        gap: '15px',
                        flexWrap: 'wrap',
                    }}
                >
                    {credits.map((credit, index) => (
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                width: '200px',
                                border: '1px solid black',
                                color: 'black',
                                textTransform: 'capitalize',
                                gap: '10px',
                                borderRadius: '15px',
                                padding: '10px',
                                textAlign: 'center',
                            }}
                            key={index}
                        >
                            <p
                                style={{
                                    fontSize: '24px',
                                }}
                            >
                                {credit.creditType}
                            </p>
                            <div
                                style={{
                                    display: 'flex',
                                    gap: '5px',
                                    justifyContent: 'center',
                                }}
                            >
                                <p>Credits: </p>
                                <span>{credit.balance}</span>
                            </div>
                        </div>
                    ))}

                    {credits.length < 1 && (
                        <div
                            style={{
                                display: 'flex',
                                gap: '15px',
                                flexWrap: 'wrap',
                            }}
                        >
                            <div
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    width: '200px',
                                    border: '1px solid black',
                                    color: 'black',
                                    textTransform: 'capitalize',
                                    gap: '10px',
                                    borderRadius: '15px',
                                    padding: '10px',
                                    textAlign: 'center',
                                }}
                            >
                                <p
                                    style={{
                                        fontSize: '24px',
                                    }}
                                >
                                    Image
                                </p>
                                <div
                                    style={{
                                        display: 'flex',
                                        gap: '5px',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <p>Credits: </p>
                                    <span>0</span>
                                </div>
                            </div>
                            <div
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    width: '200px',
                                    border: '1px solid black',
                                    color: 'black',
                                    textTransform: 'capitalize',
                                    gap: '10px',
                                    borderRadius: '15px',
                                    padding: '10px',
                                    textAlign: 'center',
                                }}
                            >
                                <p
                                    style={{
                                        fontSize: '24px',
                                    }}
                                >
                                    Model
                                </p>
                                <div
                                    style={{
                                        display: 'flex',
                                        gap: '5px',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <p>Credits: </p>
                                    <span>0</span>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                <h2
                    style={{
                        fontSize: '24px',
                    }}
                >
                    Add Credits to User
                </h2>
                <div
                    style={{
                        display: 'flex',
                        gap: '20px',
                    }}
                >
                    <CreditsForm
                        handleAddCredits={handleAddCredits}
                        creditType="image"
                    />
                    <CreditsForm
                        handleAddCredits={handleAddCredits}
                        creditType="model"
                    />
                </div>
            </div>
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '20px',
                    padding: '10px',
                }}
            >
                <h2
                    style={{
                        fontSize: '32px',
                        marginTop: '20px',
                    }}
                >
                    {' '}
                    Last images made by{' '}
                    <span style={{ fontWeight: '700' }}>
                        {props.record.params.name}
                    </span>
                </h2>
                <div
                    style={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: '15px',
                        marginTop: '10px',
                    }}
                >
                    {images.map((image) => (
                        <img
                            src={image.imageVersions['1920x1920']}
                            key={image.id}
                            style={{
                                maxWidth: '300px',
                                maxHeight: '300px',
                            }}
                        />
                    ))}
                </div>
            </div>
        </>
    );
};

export default UserDetailEdit;
