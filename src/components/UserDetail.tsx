import { Show } from 'adminjs';
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import CreditsForm from './CreditsForm.js';

const UserDetail = (props) => {
    const [credits, setCredits] = useState([]);
    const [images, setImages] = useState([]);
    const [subscription, setSubscription] = useState({
        id: '',
        name: '',
        status: '',
        expiresAt: '',
    });
    const [statusCode, setStatusCode] = useState(0);

    const handleAddCredits = async (value) => {
        const loadImage = async () => {
            const body = {
                username: props.record.params.username,
                amount: '0',
                expiresAt: new Date().toISOString().split('T')[0],
                creditType: 'image',
            };
            const { data } = await axios.post(
                process.env.API_URL + `/internal/user-credit-balance`,
                body,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setCredits(data);
        };

        const loadModel = async () => {
            const body = {
                username: props.record.params.username,
                amount: '0',
                expiresAt: new Date().toISOString().split('T')[0],
                creditType: 'model',
            };
            const { data } = await axios.post(
                process.env.API_URL + `/internal/user-credit-balance`,
                body,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setCredits(data);
        };

        const body = {
            username: props.record.params.username,
            ...value,
        };
        const { data } = await axios.post(
            process.env.API_URL + `/internal/user-credit-balance`,
            body,
            {
                headers: {
                    'x-api-key': process.env.API_KEY,
                },
            },
        );
        setCredits(data);

        if (value.creditType == 'model') {
            loadImage();
        } else {
            loadModel();
        }
    };

    useEffect(() => {
        const loadCredits = async () => {
            const body = {
                username: props.record.params.username,
                amount: '0',
                expiresAt: new Date().toISOString().split('T')[0],
                creditType: 'image',
            };
            const { data } = await axios.post(
                process.env.API_URL + `/internal/user-credit-balance`,
                body,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setCredits(data);
        };
        // loadFullCredits();
        loadCredits();
    }, []);

    //http://127.0.0.1:3000/internal/image_completions?sortBy=createdAt&sortOrder=DESC&page=1&limit=10&userId=1f0df329-ea0d-4b3e-a12a-a3b74983b88e

    useEffect(() => {
        const loadImages = async () => {
            const { data } = await axios.get(
                process.env.API_URL +
                    `/internal/image_completions?sortBy=createdAt&sortOrder=DESC&page=1&limit=10&userId=${props.record.id}&status=ready`,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setImages(data);
        };
        loadImages();
    }, []);

    const handleAddSubscription = async () => {
        try {
            const { data } = await axios.post(
                process.env.API_URL +
                    `/internal/subscriptions/trial/${props.record.id}`,
                {},
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setSubscription(data);
            setStatusCode(0);
        } catch (error) {
            console.error(error);
        }
    };

    const handleCancelSubscription = async (subscription) => {
        await axios.delete(
            process.env.API_URL + `/internal/subscriptions/${subscription.id}`,
            {
                headers: {
                    'x-api-key': process.env.API_KEY,
                },
            },
        );
        setSubscription({
            id: '',
            name: '',
            status: '',
            expiresAt: '',
        });
        setStatusCode(404);
    };

    useEffect(() => {
        const loadSubscription = async () => {
            try {
                const { data } = await axios.get(
                    process.env.API_URL +
                        `/internal/subscriptions/${props.record.id}`,
                    {
                        headers: {
                            'x-api-key': process.env.API_KEY,
                        },
                    },
                );
                setSubscription(data);
                setStatusCode(0);
            } catch (error) {
                console.log(error);
                setStatusCode(error.response.data.statusCode);
                setSubscription({
                    id: '',
                    name: '',
                    status: '',
                    expiresAt: '',
                });
            }
        };
        loadSubscription();
    }, []);

    return (
        <>
            <Show {...props} />
            <div
                style={{
                    paddingLeft: '32px',
                    paddingBottom: '32px',
                    backgroundColor: 'white',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '15px',
                    marginTop: '-25px',
                }}
            >
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '5px',
                    }}
                >
                    <p style={{ fontSize: '12px', color: 'gray' }}>
                        Image tutorial steps
                    </p>
                    <p>
                        {props.record.params['tutorialSteps.image']
                            ? props.record.params['tutorialSteps.image']
                            : 0}
                    </p>
                </div>
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '5px',
                    }}
                >
                    <p style={{ fontSize: '12px', color: 'gray' }}>
                        Model tutorial steps
                    </p>
                    <p>
                        {props.record.params['tutorialSteps.model']
                            ? props.record.params['tutorialSteps.model']
                            : 0}
                    </p>
                </div>
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '5px',
                    }}
                >
                    <p style={{ fontSize: '12px', color: 'gray' }}>
                        Feed tutorial steps
                    </p>
                    <p>
                        {props.record.params['tutorialSteps.feed']
                            ? props.record.params['tutorialSteps.feed']
                            : 0}
                    </p>
                </div>
            </div>
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '20px',
                    padding: '10px',
                }}
            >
                <h2
                    style={{
                        fontSize: '32px',
                        marginTop: '20px',
                    }}
                >
                    Subscription
                </h2>
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '5px',
                        padding: '20px 0px',
                    }}
                >
                    {(statusCode == 404 ||
                        subscription?.status == 'inactive') && (
                        <div
                            style={{
                                display: 'flex',
                                gap: '15px',
                                width: '320px',
                                flexDirection: 'column',
                            }}
                        >
                            <p style={{ fontSize: '24px', color: 'gray' }}>
                                No active subscription
                            </p>
                            <button
                                onClick={handleAddSubscription}
                                style={{
                                    padding: '10px',
                                    fontSize: '16px',
                                    borderRadius: '10px',
                                    border: '1px solid gray',
                                    cursor: 'pointer',
                                }}
                            >
                                Add subscription
                            </button>
                        </div>
                    )}
                    {subscription?.status == 'active' && (
                        <div
                            style={{
                                display: 'flex',
                                gap: '15px',
                                width: '320px',
                                flexDirection: 'column',
                            }}
                        >
                            <div
                                style={{
                                    fontSize: '16px',
                                    gap: '10px',
                                    display: 'flex',
                                    flexDirection: 'column',
                                }}
                            >
                                <h2 style={{ fontSize: '24px' }}>
                                    Active subscription
                                </h2>
                                <p>
                                    Subscription plan:{' '}
                                    <span
                                        style={{
                                            fontWeight: 'bold',
                                            color: 'gray',
                                        }}
                                    >
                                        {subscription.name}
                                    </span>
                                </p>
                                <p>
                                    Expires at:{' '}
                                    <span
                                        style={{
                                            fontWeight: 'bold',
                                            color: 'gray',
                                        }}
                                    >
                                        {subscription.expiresAt}
                                    </span>
                                </p>
                            </div>
                            {subscription.name == 'Beginner' && (
                                <button
                                    onClick={() =>
                                        handleCancelSubscription(subscription)
                                    }
                                    style={{
                                        padding: '10px',
                                        fontSize: '16px',
                                        borderRadius: '10px',
                                        border: '1px solid gray',
                                        cursor: 'pointer',
                                    }}
                                >
                                    Cancel subscription
                                </button>
                            )}
                        </div>
                    )}
                </div>
                <h2
                    style={{
                        fontSize: '32px',
                        marginTop: '20px',
                    }}
                >
                    User Credits
                </h2>
                <div
                    style={{
                        display: 'flex',
                        gap: '15px',
                        flexWrap: 'wrap',
                    }}
                >
                    {credits.map((credit, index) => (
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                width: '200px',
                                border: '1px solid black',
                                color: 'black',
                                textTransform: 'capitalize',
                                gap: '10px',
                                borderRadius: '15px',
                                padding: '10px',
                                textAlign: 'center',
                            }}
                            key={index}
                        >
                            <p
                                style={{
                                    fontSize: '24px',
                                }}
                            >
                                {credit.creditType}
                            </p>
                            <div
                                style={{
                                    display: 'flex',
                                    gap: '5px',
                                    justifyContent: 'center',
                                }}
                            >
                                <p>Credits: </p>
                                <span>{credit.balance}</span>
                            </div>
                        </div>
                    ))}

                    {credits.length < 1 && (
                        <div
                            style={{
                                display: 'flex',
                                gap: '15px',
                                flexWrap: 'wrap',
                            }}
                        >
                            <div
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    width: '200px',
                                    border: '1px solid black',
                                    color: 'black',
                                    textTransform: 'capitalize',
                                    gap: '10px',
                                    borderRadius: '15px',
                                    padding: '10px',
                                    textAlign: 'center',
                                }}
                            >
                                <p
                                    style={{
                                        fontSize: '24px',
                                    }}
                                >
                                    Image
                                </p>
                                <div
                                    style={{
                                        display: 'flex',
                                        gap: '5px',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <p>Credits: </p>
                                    <span>0</span>
                                </div>
                            </div>
                            <div
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    width: '200px',
                                    border: '1px solid black',
                                    color: 'black',
                                    textTransform: 'capitalize',
                                    gap: '10px',
                                    borderRadius: '15px',
                                    padding: '10px',
                                    textAlign: 'center',
                                }}
                            >
                                <p
                                    style={{
                                        fontSize: '24px',
                                    }}
                                >
                                    Model
                                </p>
                                <div
                                    style={{
                                        display: 'flex',
                                        gap: '5px',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <p>Credits: </p>
                                    <span>0</span>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                <h2
                    style={{
                        fontSize: '24px',
                    }}
                >
                    Add Credits to User
                </h2>
                <div
                    style={{
                        display: 'flex',
                        gap: '20px',
                    }}
                >
                    <CreditsForm
                        handleAddCredits={handleAddCredits}
                        creditType="image"
                    />
                    <CreditsForm
                        handleAddCredits={handleAddCredits}
                        creditType="model"
                    />
                </div>
            </div>
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '20px',
                    padding: '10px',
                }}
            >
                <h2
                    style={{
                        fontSize: '32px',
                        marginTop: '20px',
                    }}
                >
                    {' '}
                    Last images made by{' '}
                    <span style={{ fontWeight: '700' }}>
                        {props.record.params.name}
                    </span>
                </h2>
                <div
                    style={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: '15px',
                        marginTop: '10px',
                    }}
                >
                    {images.map((image) => (
                        <a
                            href={`/admin/resources/ImageCompletion/records/${image.id}/show`}
                            style={{ cursor: 'pointer' }}
                        >
                            <img
                                src={image.imageVersions['1920x1920']}
                                key={image.id}
                                style={{
                                    maxWidth: '300px',
                                    maxHeight: '300px',
                                }}
                            />
                        </a>
                    ))}
                </div>
            </div>
        </>
    );
};

export default UserDetail;
