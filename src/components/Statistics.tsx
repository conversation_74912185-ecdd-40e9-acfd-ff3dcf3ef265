import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Bar<PERSON>hart from './BarChart.js';
import YearlyStatisticsForm from './YearlyStatisticsForm.js';
import MonthlyStatisticsForm from './MonthlyStatisticsForm.js';

const Statistics = () => {
    const [statistics, setStatistics] = useState({
        imagesCreated: 0,
        modelsCreated: 0,
        userSignUps: 0,
        usersActive: 0,
    });
    const [topUsers, setTopUsers] = useState([]);

    const now = new Date();
    const defaultSpecific = new Date();
    defaultSpecific.setDate(defaultSpecific.getDate() - 60);
    const [initialSpecificDate, setInitialSpecificDate] =
        useState(defaultSpecific);
    const [endSpecificDate, setEndSpecificDate] = useState(new Date());
    const [year, setYear] = useState(now.getFullYear());
    const [year2, setYear2] = useState(now.getFullYear());
    const [month, setMonth] = useState(now.getMonth());

    const [yearlyImageData, setYearlyImageData] = useState([0]);
    const [yearlyModelData, setYearlyModelData] = useState([0]);
    const [yearlyUsersData, setYearlyUsersData] = useState([0]);
    const [yearlyActiveUsersData, setYearlyActiveUsersData] = useState([0]);

    const [monthlyImageData, setMonthlyImageData] = useState([0]);
    const [monthlyModelData, setMonthlyModelData] = useState([0]);
    const [monthlyUsersData, setMonthlyUsersData] = useState([0]);
    const [monthlyActiveUsersData, setMonthlyActiveUsersData] = useState([0]);
    const [monthDays, setMonthDays] = useState([1]);

    const monthLabels = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
    ];

    const imagesColor = ['#ed9889'];
    const modelsColor = ['#5375da'];
    const usersColor = ['#80c87d'];
    const activeUsersColor = ['#477445'];

    useEffect(() => {
        const today = new Date();
        const images = [];
        const models = [];
        const users = [];
        const activeUsers = [];

        const loadYearlyStatistics = async () => {
            const { data } = await axios.get(
                process.env.API_URL +
                    `/internal/statistics/yearly/${today.getFullYear()}`,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            for (let index = 0; index < 12; index++) {
                images.push(0);
                models.push(0);
                users.push(0);
                activeUsers.push(0);
            }
            for (let index = 0; index < data.imagesCreated.length; index++) {
                for (
                    let index = 0;
                    index < data.imagesCreated.length;
                    index++
                ) {
                    let monthOfYear = data.imagesCreated[index].month - 1;
                    images[monthOfYear] = data.imagesCreated[index].amount;
                }

                for (
                    let index = 0;
                    index < data.modelsCreated.length;
                    index++
                ) {
                    let monthOfYear = data.modelsCreated[index].month - 1;
                    models[monthOfYear] = data.modelsCreated[index].amount;
                }
                for (let index = 0; index < data.userSignUps.length; index++) {
                    let monthOfYear = data.userSignUps[index].month - 1;
                    users[monthOfYear] = data.userSignUps[index].amount;
                }
                for (let index = 0; index < data.usersActive.length; index++) {
                    let monthOfYear = data.usersActive[index].month - 1;
                    activeUsers[monthOfYear] = data.usersActive[index].amount;
                }
            }
            setYearlyImageData(images);
            setYearlyModelData(models);
            setYearlyUsersData(users);
            setYearlyActiveUsersData(activeUsers);
        };
        loadYearlyStatistics();
    }, []);

    useEffect(() => {
        const today = new Date();
        const images = [];
        const models = [];
        const users = [];
        const activeUsers = [];
        const days = [];

        const loadMonthlyStatistics = async () => {
            const { data } = await axios.get(
                process.env.API_URL +
                    `/internal/statistics/monthly/${today.getFullYear()}/${
                        today.getMonth() + 1
                    }`,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            for (let index = 0; index < 31; index++) {
                images.push(0);
                models.push(0);
                users.push(0);
                activeUsers.push(0);
                days.push(index + 1);
            }
            for (let index = 0; index < data.imagesCreated.length; index++) {
                let dayOfMonth = data.imagesCreated[index].day - 1;
                images[dayOfMonth] = data.imagesCreated[index].amount;
            }

            for (let index = 0; index < data.modelsCreated.length; index++) {
                let dayOfMonth = data.modelsCreated[index].day - 1;
                models[dayOfMonth] = data.modelsCreated[index].amount;
            }
            for (let index = 0; index < data.userSignUps.length; index++) {
                let dayOfMonth = data.userSignUps[index].day - 1;
                users[dayOfMonth] = data.userSignUps[index].amount;
            }
            for (let index = 0; index < data.usersActive.length; index++) {
                let dayOfMonth = data.usersActive[index].day - 1;
                activeUsers[dayOfMonth] = data.usersActive[index].amount;
            }
            setMonthlyImageData(images);
            setMonthlyModelData(models);
            setMonthlyUsersData(users);
            setMonthlyActiveUsersData(activeUsers);
            setMonthDays(days);
        };
        loadMonthlyStatistics();
    }, []);

    useEffect(() => {
        const endDate = new Date();
        const initialDate = new Date();
        initialDate.setDate(endDate.getDate() - 60);
        const loadStatistics = async () => {
            const { data } = await axios.get(
                process.env.API_URL +
                    `/internal/statistics?initialDate=${
                        initialDate.toISOString().split('T')[0]
                    }&endDate=${endDate.toISOString().split('T')[0]}`,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setStatistics(data);
        };
        loadStatistics();
    }, []);

    useEffect(() => {
        const loadTopUsers = async () => {
            try {
                const { data } = await axios.get(
                    process.env.API_URL + `/internal/stats/users/top-model`,
                    {
                        headers: {
                            'x-api-key': process.env.API_KEY,
                        },
                    },
                );
                setTopUsers(data);
            } catch (error) {
                console.error('Error loading top users:', error);
            }
        };

        loadTopUsers();
    }, []);

    const handleMonthlyStatistics = async (year, month) => {
        const images = [];
        const models = [];
        const users = [];
        const activeUsers = [];
        const days = [];
        const { data } = await axios.get(
            process.env.API_URL +
                `/internal/statistics/monthly/${year}/${month}`,
            {
                headers: {
                    'x-api-key': process.env.API_KEY,
                },
            },
        );
        for (let index = 0; index < 31; index++) {
            images.push(0);
            models.push(0);
            users.push(0);
            activeUsers.push(0);
            days.push(index + 1);
        }
        for (let index = 0; index < data.imagesCreated.length; index++) {
            let dayOfMonth = data.imagesCreated[index].day - 1;
            images[dayOfMonth] = data.imagesCreated[index].amount;
        }
        for (let index = 0; index < data.modelsCreated.length; index++) {
            let dayOfMonth = data.modelsCreated[index].day - 1;
            models[dayOfMonth] = data.modelsCreated[index].amount;
        }
        for (let index = 0; index < data.userSignUps.length; index++) {
            let dayOfMonth = data.userSignUps[index].day - 1;
            users[dayOfMonth] = data.userSignUps[index].amount;
        }
        for (let index = 0; index < data.usersActive.length; index++) {
            let dayOfMonth = data.usersActive[index].day - 1;
            activeUsers[dayOfMonth] = data.usersActive[index].amount;
        }
        setMonthlyImageData(images);
        setMonthlyModelData(models);
        setMonthlyUsersData(users);
        setMonthlyActiveUsersData(activeUsers);
        setMonthDays(days);
    };

    const handleYearlyStatistics = async (year) => {
        const images = [];
        const models = [];
        const users = [];
        const activeUsers = [];

        const { data } = await axios.get(
            process.env.API_URL + `/internal/statistics/yearly/${year}`,
            {
                headers: {
                    'x-api-key': process.env.API_KEY,
                },
            },
        );
        for (let index = 0; index < 12; index++) {
            images.push(0);
            models.push(0);
            users.push(0);
            activeUsers.push(0);
        }
        for (let index = 0; index < data.imagesCreated.length; index++) {
            for (let index = 0; index < data.imagesCreated.length; index++) {
                let monthOfYear = data.imagesCreated[index].month - 1;
                images[monthOfYear] = data.imagesCreated[index].amount;
            }

            for (let index = 0; index < data.modelsCreated.length; index++) {
                let monthOfYear = data.modelsCreated[index].month - 1;
                models[monthOfYear] = data.modelsCreated[index].amount;
            }
            for (let index = 0; index < data.userSignUps.length; index++) {
                let monthOfYear = data.userSignUps[index].month - 1;
                users[monthOfYear] = data.userSignUps[index].amount;
            }
            for (let index = 0; index < data.usersActive.length; index++) {
                let monthOfYear = data.usersActive[index].month - 1;
                activeUsers[monthOfYear] = data.usersActive[index].amount;
            }
        }
        setYearlyImageData(images);
        setYearlyModelData(models);
        setYearlyUsersData(users);
        setYearlyActiveUsersData(activeUsers);
    };

    const handleTotalStatistics = async () => {
        const { data } = await axios.get(
            process.env.API_URL +
                `/internal/statistics?initialDate=${
                    initialSpecificDate.toISOString().split('T')[0]
                }&endDate=${endSpecificDate.toISOString().split('T')[0]}`,
            {
                headers: {
                    'x-api-key': process.env.API_KEY,
                },
            },
        );
        setStatistics(data);
    };
    return (
        <>
            <main
                style={{
                    backgroundColor: 'white',
                    padding: '32px',
                    margin: '32px',
                }}
            >
                <h1
                    style={{
                        fontSize: '32px',
                    }}
                >
                    Statistics
                </h1>
                <section
                    style={{
                        padding: '24px',
                        marginTop: '32px',
                        backgroundColor: '#f9f9f9',
                    }}
                >
                    <h2
                        style={{
                            fontSize: '24px',
                            marginBottom: '24px',
                        }}
                    >
                        Users with the most models
                    </h2>
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '16px',
                            maxHeight: '350px',
                            overflowY: 'auto',
                            marginBottom: '40px',
                            scrollbarWidth: 'thin',
                            scrollbarColor: '#888 #f1f1f1',
                            msOverflowStyle: 'none' /* IE and Edge */,
                        }}
                        className="custom-scrollbar"
                    >
                        <style>
                            {`
                            .custom-scrollbar::-webkit-scrollbar {
                                width: 6px;
                            }
                            
                            .custom-scrollbar::-webkit-scrollbar-track {
                                background: #f1f1f1;
                                border-radius: 3px;
                            }
                            
                            .custom-scrollbar::-webkit-scrollbar-thumb {
                                background: #888;
                                border-radius: 3px;
                            }
                            
                            .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                                background: #555;
                            }
                            
                            /* Hide scrollbar arrows in WebKit browsers */
                            .custom-scrollbar::-webkit-scrollbar-button {
                                display: none;
                            }
                            `}
                        </style>
                        {topUsers.map((user, index) => (
                            <div
                                key={user.userId}
                                style={{
                                    backgroundColor: 'white',
                                    padding: '20px',
                                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                                    marginBottom: '8px',
                                }}
                            >
                                <div
                                    style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'center',
                                    }}
                                >
                                    <div>
                                        <h3
                                            style={{
                                                fontSize: '18px',
                                                marginBottom: '8px',
                                            }}
                                        >
                                            {index + 1}. {user.username}
                                        </h3>
                                        <p
                                            style={{
                                                color: '#555',
                                                fontSize: '14px',
                                                marginBottom: '8px',
                                            }}
                                        >
                                            {user.email}
                                        </p>
                                    </div>
                                    <div style={{ textAlign: 'right' }}>
                                        <p
                                            style={{
                                                fontWeight: 'bold',
                                                fontSize: '16px',
                                                marginBottom: '4px',
                                            }}
                                        >
                                            {user.modelCount} Models
                                        </p>
                                        <p
                                            style={{
                                                fontSize: '14px',
                                                color: '#555',
                                            }}
                                        >
                                            {user.publicModels} public /{' '}
                                            {user.privateModels} private
                                        </p>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </section>
                <section
                    style={{
                        padding: '24px',
                        marginTop: '32px',
                    }}
                >
                    <h2
                        style={{
                            fontSize: '24px',
                            marginBottom: '10px',
                        }}
                    >
                        Total Statistics
                    </h2>
                    <div
                        style={{
                            display: 'flex',
                            gap: '10px',
                            alignItems: 'center',
                            marginTop: '24px',
                            marginBottom: '24px',
                            fontSize: '16px',
                        }}
                    >
                        <span>From: </span>
                        <input
                            type="date"
                            name=""
                            id=""
                            style={{
                                padding: '10px',
                                border: '1px solid gray',
                                borderRadius: '5px',
                            }}
                            onChange={(e) =>
                                setInitialSpecificDate(new Date(e.target.value))
                            }
                            defaultValue={
                                initialSpecificDate.toISOString().split('T')[0]
                            }
                        />
                        <span>To: </span>
                        <input
                            type="date"
                            name=""
                            id=""
                            style={{
                                padding: '10px',
                                border: '1px solid gray',
                                borderRadius: '5px',
                            }}
                            onChange={(e) =>
                                setEndSpecificDate(new Date(e.target.value))
                            }
                            defaultValue={
                                endSpecificDate.toISOString().split('T')[0]
                            }
                        />
                        <button
                            onClick={handleTotalStatistics}
                            style={{
                                padding: '10px',
                                fontSize: '16px',
                                borderRadius: '5px',
                                border: '1px solid gray',
                                cursor: 'pointer',
                            }}
                        >
                            Search Statistics
                        </button>
                    </div>
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '20px',
                        }}
                    >
                        <div
                            style={{
                                display: 'flex',
                                gap: '5px',
                                flexDirection: 'column',
                            }}
                        >
                            <h3
                                style={{
                                    fontSize: '12px',
                                    color: 'gray',
                                }}
                            >
                                Images created{' '}
                            </h3>
                            <span>{statistics.imagesCreated}</span>
                        </div>
                        <div
                            style={{
                                display: 'flex',
                                gap: '5px',
                                flexDirection: 'column',
                            }}
                        >
                            <h3
                                style={{
                                    fontSize: '12px',
                                    color: 'gray',
                                }}
                            >
                                Models created{' '}
                            </h3>
                            <span>{statistics.modelsCreated}</span>
                        </div>
                        <div
                            style={{
                                display: 'flex',
                                gap: '5px',
                                flexDirection: 'column',
                            }}
                        >
                            <h3
                                style={{
                                    fontSize: '12px',
                                    color: 'gray',
                                }}
                            >
                                Users sign up
                            </h3>
                            <span>{statistics.userSignUps}</span>
                        </div>
                        <div
                            style={{
                                display: 'flex',
                                gap: '5px',
                                flexDirection: 'column',
                            }}
                        >
                            <h3
                                style={{
                                    fontSize: '12px',
                                    color: 'gray',
                                }}
                            >
                                Active users
                            </h3>
                            <span>{statistics.usersActive}</span>
                        </div>
                    </div>
                </section>
                <h2
                    style={{
                        fontSize: '24px',
                        marginLeft: '24px',
                        marginTop: '64px',
                        marginBottom: '24px',
                    }}
                >
                    Statistics for the year {year}
                </h2>
                <YearlyStatisticsForm
                    year={year}
                    setYear={setYear}
                    handleYearlyStatistics={handleYearlyStatistics}
                />
                <section
                    style={{
                        display: 'flex',
                        gap: '40px',
                        flexWrap: 'wrap',
                    }}
                >
                    <div
                        style={{
                            width: '45%',
                        }}
                    >
                        <BarChart
                            labels={monthLabels}
                            label="Images generated"
                            colors={imagesColor}
                            statistics={yearlyImageData}
                        />
                    </div>
                    <div
                        style={{
                            width: '45%',
                        }}
                    >
                        <BarChart
                            labels={monthLabels}
                            label="Models generated"
                            colors={modelsColor}
                            statistics={yearlyModelData}
                        />
                    </div>
                    <div
                        style={{
                            width: '45%',
                        }}
                    >
                        <BarChart
                            labels={monthLabels}
                            label="Users sign ups"
                            colors={usersColor}
                            statistics={yearlyUsersData}
                        />
                    </div>
                    <div
                        style={{
                            width: '45%',
                        }}
                    >
                        <BarChart
                            labels={monthLabels}
                            label="Active users"
                            colors={activeUsersColor}
                            statistics={yearlyActiveUsersData}
                        />
                    </div>
                </section>
                <h2
                    style={{
                        fontSize: '24px',
                        marginLeft: '24px',
                        marginTop: '64px',
                        marginBottom: '24px',
                    }}
                >
                    Statistics for {monthLabels[month]} of {year2}
                </h2>
                <MonthlyStatisticsForm
                    year={year2}
                    setYear={setYear2}
                    month={month}
                    setMonth={setMonth}
                    handleMonthlyStatistics={handleMonthlyStatistics}
                />
                <section
                    style={{
                        display: 'flex',
                        gap: '40px',
                        flexWrap: 'wrap',
                    }}
                >
                    <div
                        style={{
                            width: '45%',
                        }}
                    >
                        <BarChart
                            labels={monthDays}
                            label="Images generated"
                            colors={imagesColor}
                            statistics={monthlyImageData}
                        />
                    </div>
                    <div
                        style={{
                            width: '45%',
                        }}
                    >
                        <BarChart
                            labels={monthDays}
                            label="Models generated"
                            colors={modelsColor}
                            statistics={monthlyModelData}
                        />
                    </div>
                    <div
                        style={{
                            width: '45%',
                        }}
                    >
                        <BarChart
                            labels={monthDays}
                            label="Users sign ups"
                            colors={usersColor}
                            statistics={monthlyUsersData}
                        />
                    </div>
                    <div
                        style={{
                            width: '45%',
                        }}
                    >
                        <BarChart
                            labels={monthDays}
                            label="Active users"
                            colors={activeUsersColor}
                            statistics={monthlyActiveUsersData}
                        />
                    </div>
                </section>
            </main>
        </>
    );
};

export default Statistics;
