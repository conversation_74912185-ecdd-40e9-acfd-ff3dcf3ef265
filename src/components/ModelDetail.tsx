import { Show } from 'adminjs';
import React, { useState, useEffect } from 'react';
import axios from 'axios';
const ModelDetail = (props) => {
    const [versions, setVersions] = useState([]);

    useEffect(() => {
        const loadVersions = async () => {
            const { data } = await axios.get(
                process.env.API_URL + `/internal/models/${props.record.id}`,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                },
            );
            setVersions(data.versions);
        };
        loadVersions();
    }, []);

    const convertDate = (oldDate) => {
        if (oldDate == null) {
            return 'Date not specified';
        }
        const convertedDate = new Date(oldDate).toLocaleString();
        return convertedDate;
    };

    const versionCount = (length) => {
        if (length <= 1) {
            return 'version';
        }
        return 'versions';
    };

    return (
        <>
            <Show {...props} />
            <div
                style={{
                    padding: '10px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '20px',
                    marginTop: '20px',
                }}
            >
                <h2
                    style={{
                        fontSize: '32px',
                    }}
                >
                    Versions
                </h2>
                <p
                    style={{
                        color: 'gray',
                    }}
                >
                    This model has {versions.length}{' '}
                    {versionCount(versions.length)}
                </p>
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '20px',
                        maxHeight: '700px',
                        overflowY: 'auto',
                    }}
                >
                    {versions.map((version) => (
                        <div
                            style={{
                                backgroundColor: 'white',
                                padding: '20px',
                                gap: '10px',
                                display: 'flex',
                                flexDirection: 'column',
                                border: '1px solid #cbcac7',
                                borderRadius: '25px',
                                width: '45%',
                            }}
                        >
                            <div
                                style={{
                                    display: 'flex',
                                    gap: '5px',
                                    border: '1px solid #ff6e50',
                                    color: '#ff6e50',
                                    padding: '5px',
                                    borderRadius: '10px',
                                    maxWidth: '300px',
                                    fontSize: '12px',
                                    marginBottom: '20px',
                                    justifyContent: 'center',
                                }}
                            >
                                <p>Id: </p>
                                <span>{version.id}</span>
                            </div>
                            <div
                                style={{
                                    display: 'flex',
                                    gap: '20px',
                                    justifyContent: 'space-between',
                                    flexWrap: 'wrap',
                                }}
                            >
                                <div
                                    style={{
                                        display: 'flex',
                                        gap: '5px',
                                        border: '1px solid #000000',
                                        padding: '10px',
                                        borderRadius: '10px',
                                        width: '260px',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <p>Created At: </p>
                                    <span>
                                        {convertDate(version.createdAt)}
                                    </span>
                                </div>
                                <div
                                    style={{
                                        display: 'flex',
                                        gap: '5px',
                                        border: '1px solid #507aff',
                                        color: '#507aff',
                                        padding: '10px',
                                        borderRadius: '10px',
                                        width: '140px',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <p>Status: </p>
                                    <span>{version.status}</span>
                                </div>
                            </div>
                            <div
                                style={{
                                    display: 'flex',
                                    gap: '20px',
                                    justifyContent: 'space-between',
                                }}
                            >
                                <div
                                    style={{
                                        display: 'flex',
                                        gap: '5px',
                                        border: '1px solid #000000',
                                        padding: '10px',
                                        borderRadius: '10px',
                                        flexWrap: 'wrap',
                                        width: '260px',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <p>Trained At: </p>
                                    <span>
                                        {convertDate(version.traineddAt)}
                                    </span>
                                </div>
                                <div
                                    style={{
                                        display: 'flex',
                                        gap: '5px',
                                        border: '1px solid #93730a',
                                        color: '#93730a',
                                        padding: '10px',
                                        borderRadius: '10px',
                                        width: '140px',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <p>Version</p>
                                    <span>{version.version}</span>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </>
    );
};

export default ModelDetail;
