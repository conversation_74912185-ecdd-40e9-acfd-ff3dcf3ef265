import { ComponentLoader } from 'adminjs';
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';

// Component loader for AdminJS custom components

const componentLoader = new ComponentLoader();

const __dirname = dirname(fileURLToPath(import.meta.url));
const ImageListPage = path.resolve(__dirname, './ImageList');
const ImageDetail = path.resolve(__dirname, './ImageDetail');
const ModelDetail = path.resolve(__dirname, './ModelDetail');
const VideoDetail = path.resolve(__dirname, './VideoDetail');
const UserDetail = path.resolve(__dirname, './UserDetail');
const UserDetailEdit = path.resolve(__dirname, './UserDetailEdit');
const Statistics = path.resolve(__dirname, './Statistics');
const OrganizationDetail = path.resolve(__dirname, './OrganizationDetail');
const ChangePasswordForm = path.resolve(__dirname, './ChangePasswordForm');

const Components = {
    ImageList: componentLoader.add('ImageList', ImageListPage),
    ImageDetail: componentLoader.add('ImageDetail', ImageDetail),
    ModelDetail: componentLoader.add('ModelDetail', ModelDetail),
    VideoDetail: componentLoader.add('VideoDetail', VideoDetail),
    UserDetail: componentLoader.add('UserDetail', UserDetail),
    UserDetailEdit: componentLoader.add('UserDetailEdit', UserDetailEdit),
    Statistics: componentLoader.add('Statistics', Statistics),
    OrganizationDetail: componentLoader.add(
        'OrganizationDetail',
        OrganizationDetail,
    ),
    ChangePasswordForm: componentLoader.add(
        'ChangePasswordForm',
        ChangePasswordForm,
    ),
    // other custom components
};

export { componentLoader, Components };
