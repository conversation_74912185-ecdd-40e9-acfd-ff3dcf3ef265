// import { join } from 'path';
import { DataSource } from 'typeorm';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import * as fs from 'fs';

const __dirname = dirname(fileURLToPath(import.meta.url));

export const databaseProviders = [
    {
        provide: 'DATA_SOURCE',
        useFactory: async () => {
            let sslConfig = {};

            if (process.env.NODE_ENV === 'production') {
              const caCertPath = '/usr/src/app/certs/eu-north-1-bundle.pem';
              const caCert = fs.readFileSync(caCertPath).toString();

              sslConfig = {
                ssl: {
                  rejectUnauthorized: false,
                  ca: caCert,
                },
              };
            }

            const dataSource = new DataSource({
                type: 'postgres',
                host: process.env.POSTGRES_HOST,
                port: Number(process.env.POSTGRES_PORT),
                username: process.env.POSTGRES_USER,
                password: process.env.POSTGRES_PASSWORD,
                database: process.env.POSTGRES_DB,
                // entities: [join(__dirname, '/../**/*.entity{.ts,.js}')],
                entities: [
                    join(__dirname, '**', '*.entity.{ts,js}'),
                    join(__dirname, 'admin-users', '*.entity.{ts,js}'),
                ],
                synchronize: false,
                namingStrategy: new SnakeNamingStrategy(),
                ...sslConfig,
            });

            return dataSource.initialize();
        },
    },
];
