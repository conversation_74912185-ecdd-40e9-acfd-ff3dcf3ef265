import { Module } from '@nestjs/common';
import { AppController } from './app.controller.js';
import { AppService } from './app.service.js';
import { DatabaseModule } from './database.module.js';
import { User } from './users/user.entity.js';
import { UserModule } from './users/user.module.js';
import { Model } from './model/model.entity.js';
import { ModelModule } from './model/model.module.js';
import { ModelVersion } from './model-version/model-version.entity.js';
import { ModelVersionModule } from './model-version/model-version.module.js';
import { ImageCompletion } from './image-completion/image-completion.entity.js';
import { ImageCompletionModule } from './image-completion/image-completion.module.js';
import { Board } from './boards/boards.entity.js';
import { BoardModule } from './boards/boards.module.js';
import { Upscale } from './upscale/upscale.entity.js';
import { UpscaleModule } from './upscale/upscale.module.js';
import { SignupCode } from './user-signup-code/user-signup-code.entity.js';
import { SignupCodeModule } from './user-signup-code/user-signup-code.module.js';
import { GlobalNotification } from './global-notification/global-notification.entity.js';
import { GlobalNotificationModule } from './global-notification/global-notification.module.js';
import { Organization } from './organizations/organization.entity.js';
import { OrganizationModule } from './organizations/organization.module.js';
import axios from 'axios';
import 'dotenv/config';
import * as crypto from 'crypto';

// Generate secure session secrets if not provided
const SESSION_SECRET = process.env.SESSION_SECRET || crypto.randomBytes(32).toString('hex');
const COOKIE_SECRET = process.env.COOKIE_SECRET || crypto.randomBytes(32).toString('hex');

// Enhanced authentication function will be created dynamically
let enhancedAuthenticate: any = null;

const dashboardHandler = async (req) => {
    const { id } = req.query;

    if (id) {
        const URL = `/internal/image_completions/${id}`;
        const imageUrl = await axios.get(process.env.API_URL + URL, {
            headers: {
                'x-api-key': process.env.API_KEY,
            },
        });

        return {
            imageUrl: imageUrl?.data?.imageUrl,
        };
    }

    const page = req.query.page;

    let modelList = '';
    if (req?.query?.modelList) {
        const mappedModels = req.query.modelList
            ?.map((id) => `modelIds[]=${id}`)
            ?.join('&');
        modelList = mappedModels ? '&' + mappedModels : '';
    }
    const modelpage = req.query.modelPage;
    const isHot = req.query.isHot;

    let URL =
        isHot === true || isHot === 'true'
            ? `/internal/image_completions?sortBy=createdAt&sortOrder=DESC&page=${page}&isHot=true&limit=50${modelList}`
            : `/internal/image_completions?sortBy=createdAt&sortOrder=DESC&page=${page}&limit=50${modelList}`;

    if (req.query.includeNsfw === true || req.query.includeNsfw === 'true') {
        URL += `&includeNsfw=${req.query.includeNsfw}`;
    }

    if (req.query.privacy) {
        URL += `&privacy=${req.query.privacy}`;
    }

    const images = await axios.get(process.env.API_URL + URL, {
        headers: {
            'x-api-key': process.env.API_KEY,
        },
    });

    const models = await axios.get(
        process.env.API_URL +
            `/internal/models?page=${modelpage}&sortBy=name&limit=35`,
        {
            headers: {
                'x-api-key': process.env.API_KEY,
            },
        },
    );

    return {
        images: images?.data,
        models: models?.data,
        totalModelPages: models?.headers['x-total-pages'],
        totalImagePages: images?.headers['x-total-pages'],
    };
};

@Module({
    imports: [
        DatabaseModule,
        GlobalNotificationModule,
        UserModule,
        ModelModule,
        ModelVersionModule,
        ImageCompletionModule,
        BoardModule,
        UpscaleModule,
        SignupCodeModule,
        OrganizationModule,
    ],
    controllers: [AppController],
    providers: [AppService],
})
export class SimpleAppModule {
    constructor() {
        console.log('🔐 Enhanced Security Features:');
        console.log('✅ Secure session secrets generated');
        console.log('✅ Enhanced authentication logging');
        console.log('✅ Improved cookie security');
        console.log('✅ Session timeout configured (8 hours)');
        console.log('');
        console.log('⚠️  AdminJS compatibility issue detected with Node.js v23');
        console.log('💡 Recommendation: Use Node.js 18 for full AdminJS functionality');
        console.log('');
        console.log(`🔑 Session Secret: ${SESSION_SECRET.substring(0, 8)}...`);
        console.log(`🍪 Cookie Secret: ${COOKIE_SECRET.substring(0, 8)}...`);
    }
}
