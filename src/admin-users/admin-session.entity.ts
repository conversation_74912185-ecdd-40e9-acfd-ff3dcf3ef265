import {
    <PERSON>Entity,
    Column,
    CreateDateColumn,
    Entity,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    Index,
} from 'typeorm';
import { AdminUser } from './admin-user.entity.js';

@Entity('admin_session')
export class AdminSession extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => AdminUser, { eager: true })
    adminUser: AdminUser;

    @Column({ nullable: false })
    @Index('idx_admin_session_user')
    adminUserId: string;

    @Column({ nullable: false, unique: true })
    @Index('idx_admin_session_token')
    sessionToken: string;

    @Column({ nullable: false })
    ipAddress: string;

    @Column({ type: 'text', nullable: true })
    userAgent?: string;

    @Column({ type: 'timestamp', nullable: false })
    expiresAt: Date;

    @Column({ default: true })
    isActive: boolean;

    @Column({ type: 'timestamp', nullable: true })
    lastActivityAt?: Date;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
