import {
    BaseEntity,
    Column,
    CreateDate<PERSON><PERSON>umn,
    DeleteDateColumn,
    Entity,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    Index,
} from 'typeorm';

export enum AdminRoleEnum {
    SUPER_ADMIN = 'super_admin',
    ADMIN = 'admin',
    MODERATOR = 'moderator',
    VIEWER = 'viewer',
}

export enum AdminStatusEnum {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    SUSPENDED = 'suspended',
}

@Entity('admin_user')
export class AdminUser extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ nullable: false })
    name: string;

    @Column({ nullable: false, unique: true })
    @Index('idx_admin_email')
    email: string;

    @Column({ nullable: false, select: false })
    passwordHash: string;

    // Virtual field for AdminJS forms - not persisted to database
    password?: string;

    @Column({
        type: 'enum',
        enum: AdminRoleEnum,
        default: AdminRoleEnum.VIEWER,
        nullable: false,
    })
    role: AdminRoleEnum;

    @Column({
        type: 'enum',
        enum: AdminStatusEnum,
        default: AdminStatusEnum.ACTIVE,
        nullable: false,
    })
    status: AdminStatusEnum;

    @Column({ nullable: true })
    twoFactorSecret?: string;

    @Column({ default: false })
    twoFactorEnabled: boolean;

    @Column({ type: 'json', nullable: true })
    allowedIpAddresses?: string[];

    @Column({ type: 'timestamp', nullable: true })
    lastLoginAt?: Date;

    @Column({ nullable: true })
    lastLoginIp?: string;

    @Column({ default: 0 })
    failedLoginAttempts: number;

    @Column({ type: 'timestamp', nullable: true })
    lockedUntil?: Date;

    @Column({ type: 'timestamp', nullable: true })
    passwordChangedAt?: Date;

    @Column({ default: true })
    mustChangePassword: boolean;

    @Column({ type: 'json', nullable: true })
    permissions?: string[];

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @DeleteDateColumn({ type: 'timestamp', nullable: true })
    deletedAt?: Date;
}
