import {
    BaseEntity,
    Column,
    CreateDateColumn,
    Entity,
    ManyToOne,
    PrimaryGeneratedColumn,
    Index,
} from 'typeorm';
import { AdminUser } from './admin-user.entity.js';

export enum AuditActionEnum {
    LOGIN = 'login',
    LOGOUT = 'logout',
    LOGIN_FAILED = 'login_failed',
    CREATE = 'create',
    UPDATE = 'update',
    DELETE = 'delete',
    VIEW = 'view',
    EXPORT = 'export',
    BULK_ACTION = 'bulk_action',
    SETTINGS_CHANGE = 'settings_change',
    PASSWORD_CHANGE = 'password_change',
    ROLE_CHANGE = 'role_change',
    ACCOUNT_LOCK = 'account_lock',
    ACCOUNT_UNLOCK = 'account_unlock',
}

@Entity('admin_audit_log')
export class AdminAuditLog extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => AdminUser, { nullable: true })
    adminUser?: AdminUser;

    @Column({ nullable: true })
    @Index('idx_audit_admin_user')
    adminUserId?: string;

    @Column({
        type: 'enum',
        enum: AuditActionEnum,
        nullable: false,
    })
    @Index('idx_audit_action')
    action: AuditActionEnum;

    @Column({ nullable: false })
    @Index('idx_audit_resource')
    resource: string; // e.g., 'User', 'Model', 'ImageCompletion'

    @Column({ nullable: true })
    resourceId?: string;

    @Column({ type: 'json', nullable: true })
    oldValues?: any;

    @Column({ type: 'json', nullable: true })
    newValues?: any;

    @Column({ nullable: false })
    ipAddress: string;

    @Column({ type: 'text', nullable: true })
    userAgent?: string;

    @Column({ type: 'text', nullable: true })
    description?: string;

    @Column({ default: false })
    isSuccessful: boolean;

    @Column({ type: 'text', nullable: true })
    errorMessage?: string;

    @CreateDateColumn()
    @Index('idx_audit_created_at')
    createdAt: Date;
}
