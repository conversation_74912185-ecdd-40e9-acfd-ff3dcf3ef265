import { Injectable, Inject, UnauthorizedException } from '@nestjs/common';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import * as speakeasy from 'speakeasy';
import { AdminUser, AdminRoleEnum, AdminStatusEnum } from './admin-user.entity.js';
import { AdminSession } from './admin-session.entity.js';
import { AdminAuditLog, AuditActionEnum } from './admin-audit-log.entity.js';

export interface LoginAttempt {
    email: string;
    password: string;
    ipAddress: string;
    userAgent?: string;
    twoFactorToken?: string;
}

export interface AuthResult {
    user: AdminUser;
    sessionToken: string;
    requiresTwoFactor: boolean;
}

@Injectable()
export class AdminSecurityService {
    private readonly SALT_ROUNDS = 12;
    private readonly MAX_LOGIN_ATTEMPTS = 5;
    private readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes
    private readonly SESSION_DURATION = 8 * 60 * 60 * 1000; // 8 hours
    private readonly PASSWORD_MIN_LENGTH = 12;

    constructor(
        @Inject('ADMIN_USER_REPOSITORY')
        private adminUserRepository: Repository<AdminUser>,
        @Inject('ADMIN_SESSION_REPOSITORY')
        private adminSessionRepository: Repository<AdminSession>,
        @Inject('ADMIN_AUDIT_LOG_REPOSITORY')
        private auditLogRepository: Repository<AdminAuditLog>,
    ) {}

    async hashPassword(password: string): Promise<string> {
        return bcrypt.hash(password, this.SALT_ROUNDS);
    }

    async verifyPassword(password: string, hash: string): Promise<boolean> {
        return bcrypt.compare(password, hash);
    }

    validatePasswordStrength(password: string): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (password.length < this.PASSWORD_MIN_LENGTH) {
            errors.push(`Password must be at least ${this.PASSWORD_MIN_LENGTH} characters long`);
        }

        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }

        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }

        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }

        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }

        return {
            isValid: errors.length === 0,
            errors,
        };
    }

    async authenticate(loginAttempt: LoginAttempt): Promise<AuthResult> {
        const { email, password, ipAddress, userAgent, twoFactorToken } = loginAttempt;

        // Log the login attempt
        await this.logAuditEvent({
            action: AuditActionEnum.LOGIN,
            resource: 'AdminUser',
            ipAddress,
            userAgent,
            description: `Login attempt for ${email}`,
            isSuccessful: false, // Will update if successful
        });

        // Find user by email (include passwordHash for authentication)
        const user = await this.adminUserRepository.findOne({
            where: { email: email.toLowerCase() },
            select: ['id', 'name', 'email', 'passwordHash', 'role', 'status', 'twoFactorSecret', 'twoFactorEnabled', 
                    'allowedIpAddresses', 'lastLoginAt', 'lastLoginIp', 'failedLoginAttempts', 'lockedUntil', 
                    'passwordChangedAt', 'mustChangePassword', 'permissions', 'createdAt'],
        });

        if (!user) {
            await this.logAuditEvent({
                action: AuditActionEnum.LOGIN_FAILED,
                resource: 'AdminUser',
                ipAddress,
                userAgent,
                description: `Login failed: User not found for ${email}`,
                isSuccessful: false,
            });
            throw new UnauthorizedException('Invalid credentials');
        }

        // Check if account is locked
        if (user.lockedUntil && user.lockedUntil > new Date()) {
            await this.logAuditEvent({
                adminUserId: user.id,
                action: AuditActionEnum.LOGIN_FAILED,
                resource: 'AdminUser',
                ipAddress,
                userAgent,
                description: 'Login failed: Account is locked',
                isSuccessful: false,
            });
            throw new UnauthorizedException('Account is temporarily locked');
        }

        // Check if account is active
        if (user.status !== AdminStatusEnum.ACTIVE) {
            await this.logAuditEvent({
                adminUserId: user.id,
                action: AuditActionEnum.LOGIN_FAILED,
                resource: 'AdminUser',
                ipAddress,
                userAgent,
                description: `Login failed: Account status is ${user.status}`,
                isSuccessful: false,
            });
            throw new UnauthorizedException('Account is not active');
        }

        // Check IP restrictions
        if (user.allowedIpAddresses && user.allowedIpAddresses.length > 0) {
            if (!user.allowedIpAddresses.includes(ipAddress)) {
                await this.logAuditEvent({
                    adminUserId: user.id,
                    action: AuditActionEnum.LOGIN_FAILED,
                    resource: 'AdminUser',
                    ipAddress,
                    userAgent,
                    description: 'Login failed: IP address not allowed',
                    isSuccessful: false,
                });
                throw new UnauthorizedException('Access denied from this IP address');
            }
        }

        // Verify password
        const isPasswordValid = await this.verifyPassword(password, user.passwordHash);
        if (!isPasswordValid) {
            await this.handleFailedLogin(user, ipAddress, userAgent);
            throw new UnauthorizedException('Invalid credentials');
        }

        // Check two-factor authentication
        if (user.twoFactorEnabled) {
            if (!twoFactorToken) {
                return {
                    user,
                    sessionToken: '',
                    requiresTwoFactor: true,
                };
            }

            const isValidToken = speakeasy.totp.verify({
                secret: user.twoFactorSecret!,
                encoding: 'base32',
                token: twoFactorToken,
                window: 2,
            });

            if (!isValidToken) {
                await this.logAuditEvent({
                    adminUserId: user.id,
                    action: AuditActionEnum.LOGIN_FAILED,
                    resource: 'AdminUser',
                    ipAddress,
                    userAgent,
                    description: 'Login failed: Invalid 2FA token',
                    isSuccessful: false,
                });
                throw new UnauthorizedException('Invalid two-factor authentication token');
            }
        }

        // Reset failed login attempts
        if (user.failedLoginAttempts > 0) {
            await this.adminUserRepository.update(user.id, {
                failedLoginAttempts: 0,
                lockedUntil: null,
            });
        }

        // Create session
        const sessionToken = await this.createSession(user, ipAddress, userAgent);

        // Update last login
        await this.adminUserRepository.update(user.id, {
            lastLoginAt: new Date(),
            lastLoginIp: ipAddress,
        });

        // Log successful login
        await this.logAuditEvent({
            adminUserId: user.id,
            action: AuditActionEnum.LOGIN,
            resource: 'AdminUser',
            ipAddress,
            userAgent,
            description: 'Successful login',
            isSuccessful: true,
        });

        return {
            user,
            sessionToken,
            requiresTwoFactor: false,
        };
    }

    async createSession(user: AdminUser, ipAddress: string, userAgent?: string): Promise<string> {
        const sessionToken = crypto.randomBytes(32).toString('hex');
        const expiresAt = new Date(Date.now() + this.SESSION_DURATION);

        const session = this.adminSessionRepository.create({
            adminUserId: user.id,
            sessionToken,
            ipAddress,
            userAgent,
            expiresAt,
            lastActivityAt: new Date(),
        });

        await this.adminSessionRepository.save(session);
        return sessionToken;
    }

    async validateSession(sessionToken: string, ipAddress: string): Promise<AdminUser | null> {
        const session = await this.adminSessionRepository.findOne({
            where: {
                sessionToken,
                isActive: true,
            },
            relations: ['adminUser'],
        });

        if (!session) {
            return null;
        }

        // Check if session is expired
        if (session.expiresAt < new Date()) {
            await this.adminSessionRepository.update(session.id, { isActive: false });
            return null;
        }

        // Check IP address (optional - can be disabled for mobile admins)
        if (process.env.ENFORCE_SESSION_IP === 'true' && session.ipAddress !== ipAddress) {
            await this.adminSessionRepository.update(session.id, { isActive: false });
            return null;
        }

        // Update last activity
        await this.adminSessionRepository.update(session.id, {
            lastActivityAt: new Date(),
        });

        return session.adminUser;
    }

    async logout(sessionToken: string, ipAddress: string, userAgent?: string): Promise<void> {
        const session = await this.adminSessionRepository.findOne({
            where: { sessionToken },
            relations: ['adminUser'],
        });

        if (session) {
            await this.adminSessionRepository.update(session.id, { isActive: false });

            await this.logAuditEvent({
                adminUserId: session.adminUserId,
                action: AuditActionEnum.LOGOUT,
                resource: 'AdminUser',
                ipAddress,
                userAgent,
                description: 'User logged out',
                isSuccessful: true,
            });
        }
    }

    async handleFailedLogin(user: AdminUser, ipAddress: string, userAgent?: string): Promise<void> {
        const failedAttempts = user.failedLoginAttempts + 1;
        const updateData: Partial<AdminUser> = {
            failedLoginAttempts: failedAttempts,
        };

        if (failedAttempts >= this.MAX_LOGIN_ATTEMPTS) {
            updateData.lockedUntil = new Date(Date.now() + this.LOCKOUT_DURATION);

            await this.logAuditEvent({
                adminUserId: user.id,
                action: AuditActionEnum.ACCOUNT_LOCK,
                resource: 'AdminUser',
                ipAddress,
                userAgent,
                description: `Account locked after ${failedAttempts} failed login attempts`,
                isSuccessful: true,
            });
        }

        await this.adminUserRepository.update(user.id, updateData);

        await this.logAuditEvent({
            adminUserId: user.id,
            action: AuditActionEnum.LOGIN_FAILED,
            resource: 'AdminUser',
            ipAddress,
            userAgent,
            description: `Login failed: Invalid password (attempt ${failedAttempts})`,
            isSuccessful: false,
        });
    }

    async logAuditEvent(event: Partial<AdminAuditLog>): Promise<void> {
        const auditLog = this.auditLogRepository.create(event);
        await this.auditLogRepository.save(auditLog);
    }

    async generateTwoFactorSecret(): Promise<{ secret: string; qrCode: string }> {
        const secret = speakeasy.generateSecret({
            name: 'Letz.ai Admin',
            issuer: 'Letz.ai',
        });

        return {
            secret: secret.base32,
            qrCode: secret.otpauth_url!,
        };
    }

    async enableTwoFactor(userId: string, secret: string, token: string): Promise<boolean> {
        const isValid = speakeasy.totp.verify({
            secret,
            encoding: 'base32',
            token,
            window: 2,
        });

        if (isValid) {
            await this.adminUserRepository.update(userId, {
                twoFactorSecret: secret,
                twoFactorEnabled: true,
            });
        }

        return isValid;
    }

    async hasPermission(user: AdminUser, permission: string): Promise<boolean> {
        // Super admin has all permissions
        if (user.role === AdminRoleEnum.SUPER_ADMIN) {
            return true;
        }

        // Check specific permissions
        if (user.permissions && user.permissions.includes(permission)) {
            return true;
        }

        // Check role-based permissions
        const rolePermissions = this.getRolePermissions(user.role);
        return rolePermissions.includes(permission);
    }

    private getRolePermissions(role: AdminRoleEnum): string[] {
        const permissions = {
            [AdminRoleEnum.SUPER_ADMIN]: ['*'], // All permissions
            [AdminRoleEnum.ADMIN]: [
                'users.view', 'users.edit', 'users.delete',
                'models.view', 'models.edit', 'models.delete',
                'images.view', 'images.edit', 'images.delete',
                'organizations.view', 'organizations.edit',
                'statistics.view',
                'audit.view',
            ],
            [AdminRoleEnum.MODERATOR]: [
                'users.view', 'users.edit',
                'models.view', 'models.edit',
                'images.view', 'images.edit',
                'organizations.view',
                'statistics.view',
            ],
            [AdminRoleEnum.VIEWER]: [
                'users.view',
                'models.view',
                'images.view',
                'organizations.view',
                'statistics.view',
            ],
        };

        return permissions[role] || [];
    }
}
