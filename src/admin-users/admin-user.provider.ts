import { DataSource } from 'typeorm';
import { AdminUser } from './admin-user.entity.js';
import { AdminSession } from './admin-session.entity.js';
import { AdminAuditLog } from './admin-audit-log.entity.js';

export const adminUserProviders = [
    {
        provide: 'ADMIN_USER_REPOSITORY',
        useFactory: (dataSource: DataSource) =>
            dataSource.getRepository(AdminUser),
        inject: ['DATA_SOURCE'],
    },
    {
        provide: 'ADMIN_SESSION_REPOSITORY',
        useFactory: (dataSource: DataSource) =>
            dataSource.getRepository(AdminSession),
        inject: ['DATA_SOURCE'],
    },
    {
        provide: 'ADMIN_AUDIT_LOG_REPOSITORY',
        useFactory: (dataSource: DataSource) =>
            dataSource.getRepository(AdminAuditLog),
        inject: ['DATA_SOURCE'],
    },
];
