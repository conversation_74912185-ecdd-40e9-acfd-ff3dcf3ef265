import { Controller, Post, Get, Body, Param, Put, Delete, UseGuards, Request, UnauthorizedException } from '@nestjs/common';
import { AdminUserService, CreateAdminUserDto, UpdateAdminUserDto } from './admin-user.service.js';
import { AdminUser } from './admin-user.entity.js';
import { AdminSecurityService } from './admin-security.service.js';
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';

@Injectable()
class AdminAuthGuard implements CanActivate {
    constructor(private adminSecurityService: AdminSecurityService) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const session = request.session;
        
        if (!session || !session.sessionToken) {
            throw new UnauthorizedException('No valid session found');
        }

        const ipAddress = request.ip || request.connection?.remoteAddress || 'unknown';
        const user = await this.adminSecurityService.validateSession(session.sessionToken, ipAddress);
        
        if (!user) {
            throw new UnauthorizedException('Invalid session');
        }

        // Add user to request for use in controllers
        request.user = user;
        return true;
    }
}

@Controller('api/admin-users')
@UseGuards(AdminAuthGuard)
export class AdminUserController {
    constructor(private readonly adminUserService: AdminUserService) {}

    @Post()
    async createUser(@Body() createUserDto: CreateAdminUserDto): Promise<AdminUser> {
        return this.adminUserService.createAdminUser(createUserDto);
    }

    @Get()
    async getAllUsers(): Promise<AdminUser[]> {
        return this.adminUserService.findAll();
    }

    @Get(':id')
    async getUser(@Param('id') id: string): Promise<AdminUser | null> {
        return this.adminUserService.findById(id);
    }

    @Put(':id')
    async updateUser(
        @Param('id') id: string,
        @Body() updateUserDto: UpdateAdminUserDto,
    ): Promise<AdminUser> {
        return this.adminUserService.updateAdminUser(id, updateUserDto);
    }

    @Post(':id/change-password')
    async changePassword(
        @Param('id') id: string,
        @Body() body: { currentPassword: string; newPassword: string },
    ): Promise<{ message: string }> {
        await this.adminUserService.changePassword(id, body.currentPassword, body.newPassword);
        return { message: 'Password changed successfully' };
    }

    @Post(':id/reset-password')
    async resetPassword(
        @Param('id') id: string,
        @Body() body: { newPassword: string },
    ): Promise<{ message: string }> {
        await this.adminUserService.resetPassword(id, body.newPassword);
        return { message: 'Password reset successfully' };
    }

    @Post(':id/lock')
    async lockUser(@Param('id') id: string): Promise<{ message: string }> {
        await this.adminUserService.lockUser(id);
        return { message: 'User locked successfully' };
    }

    @Post(':id/unlock')
    async unlockUser(@Param('id') id: string): Promise<{ message: string }> {
        await this.adminUserService.unlockUser(id);
        return { message: 'User unlocked successfully' };
    }

    @Delete(':id')
    async deleteUser(@Param('id') id: string): Promise<{ message: string }> {
        await this.adminUserService.deleteAdminUser(id);
        return { message: 'User deleted successfully' };
    }
}
