import { Module } from '@nestjs/common';
import { DatabaseModule } from '../database.module.js';
import { adminUserProviders } from './admin-user.provider.js';
import { AdminUserService } from './admin-user.service.js';
import { AdminSecurityService } from './admin-security.service.js';
import { AdminUserController } from './admin-user.controller.js';

@Module({
    imports: [DatabaseModule],
    controllers: [AdminUserController],
    providers: [...adminUserProviders, AdminUserService, AdminSecurityService],
    exports: [AdminUserService, AdminSecurityService],
})
export class AdminUserModule {}
