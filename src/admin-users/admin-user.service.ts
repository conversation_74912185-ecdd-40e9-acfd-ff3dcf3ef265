import { Injectable, Inject, BadRequestException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { AdminUser, AdminRoleEnum, AdminStatusEnum } from './admin-user.entity.js';
import { AdminSecurityService } from './admin-security.service.js';

export interface CreateAdminUserDto {
    name: string;
    email: string;
    password: string;
    role: AdminRoleEnum;
    allowedIpAddresses?: string[];
    permissions?: string[];
}

export interface UpdateAdminUserDto {
    name?: string;
    email?: string;
    role?: AdminRoleEnum;
    status?: AdminStatusEnum;
    allowedIpAddresses?: string[];
    permissions?: string[];
}

@Injectable()
export class AdminUserService {
    constructor(
        @Inject('ADMIN_USER_REPOSITORY')
        private adminUserRepository: Repository<AdminUser>,
        private securityService: AdminSecurityService,
    ) {}

    async createAdminUser(createDto: CreateAdminUserDto): Promise<AdminUser> {
        const { name, email, password, role, allowedIpAddresses, permissions } = createDto;

        // Validate password strength
        const passwordValidation = this.securityService.validatePasswordStrength(password);
        if (!passwordValidation.isValid) {
            throw new BadRequestException(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
        }

        // Check if email already exists
        const existingUser = await this.adminUserRepository.findOne({
            where: { email: email.toLowerCase() },
        });

        if (existingUser) {
            throw new BadRequestException('Email already exists');
        }

        // Hash password
        const passwordHash = await this.securityService.hashPassword(password);

        // Create user
        const adminUser = this.adminUserRepository.create({
            name,
            email: email.toLowerCase(),
            passwordHash,
            role,
            allowedIpAddresses,
            permissions,
            passwordChangedAt: new Date(),
            mustChangePassword: false, // Since it's a new account with a strong password
        });

        return this.adminUserRepository.save(adminUser);
    }

    async updateAdminUser(id: string, updateDto: UpdateAdminUserDto): Promise<AdminUser> {
        const user = await this.adminUserRepository.findOne({ where: { id } });
        if (!user) {
            throw new BadRequestException('Admin user not found');
        }

        // If email is being updated, check for duplicates
        if (updateDto.email && updateDto.email !== user.email) {
            const existingUser = await this.adminUserRepository.findOne({
                where: { email: updateDto.email.toLowerCase() },
            });
            if (existingUser) {
                throw new BadRequestException('Email already exists');
            }
            updateDto.email = updateDto.email.toLowerCase();
        }

        await this.adminUserRepository.update(id, updateDto);
        return this.adminUserRepository.findOne({ where: { id } })!;
    }

    async changePassword(id: string, currentPassword: string, newPassword: string): Promise<void> {
        const user = await this.findByIdWithPassword(id);
        if (!user) {
            throw new BadRequestException('Admin user not found');
        }

        // Verify current password
        const isCurrentPasswordValid = await this.securityService.verifyPassword(currentPassword, user.passwordHash);
        if (!isCurrentPasswordValid) {
            throw new BadRequestException('Current password is incorrect');
        }

        // Validate new password strength
        const passwordValidation = this.securityService.validatePasswordStrength(newPassword);
        if (!passwordValidation.isValid) {
            throw new BadRequestException(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
        }

        // Hash new password
        const newPasswordHash = await this.securityService.hashPassword(newPassword);

        // Update password
        await this.adminUserRepository.update(id, {
            passwordHash: newPasswordHash,
            passwordChangedAt: new Date(),
            mustChangePassword: false,
        });
    }

    async resetPassword(id: string, newPassword: string): Promise<void> {
        // Validate password strength
        const passwordValidation = this.securityService.validatePasswordStrength(newPassword);
        if (!passwordValidation.isValid) {
            throw new BadRequestException(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
        }

        // Hash password
        const passwordHash = await this.securityService.hashPassword(newPassword);

        // Update password and force change on next login
        await this.adminUserRepository.update(id, {
            passwordHash,
            passwordChangedAt: new Date(),
            mustChangePassword: true,
            failedLoginAttempts: 0,
            lockedUntil: null,
        });
    }

    async lockUser(id: string): Promise<void> {
        await this.adminUserRepository.update(id, {
            status: AdminStatusEnum.SUSPENDED,
            lockedUntil: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        });
    }

    async unlockUser(id: string): Promise<void> {
        await this.adminUserRepository.update(id, {
            status: AdminStatusEnum.ACTIVE,
            lockedUntil: null,
            failedLoginAttempts: 0,
        });
    }

    async findAll(): Promise<AdminUser[]> {
        return this.adminUserRepository.find({
            select: ['id', 'name', 'email', 'role', 'status', 'lastLoginAt', 'createdAt'],
        });
    }

    async findById(id: string): Promise<AdminUser | null> {
        return this.adminUserRepository.findOne({
            where: { id },
            select: ['id', 'name', 'email', 'role', 'status', 'lastLoginAt', 'allowedIpAddresses', 'permissions', 'twoFactorEnabled', 'createdAt'],
        });
    }

    async findByIdWithPassword(id: string): Promise<AdminUser | null> {
        return this.adminUserRepository.findOne({
            where: { id },
            select: ['id', 'name', 'email', 'passwordHash', 'role', 'status', 'lastLoginAt', 'allowedIpAddresses', 'permissions', 'twoFactorEnabled', 'createdAt'],
        });
    }

    async deleteAdminUser(id: string): Promise<void> {
        // Soft delete
        await this.adminUserRepository.softDelete(id);
    }
}
