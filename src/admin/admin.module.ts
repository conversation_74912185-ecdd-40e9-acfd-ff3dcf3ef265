import { Module } from '@nestjs/common';
import { AdminModule } from '@adminjs/nestjs';
import AdminJS from 'adminjs';
import * as AdminJSTypeorm from '@adminjs/typeorm';
import { User } from '../users/user.entity.js';
import { AdminUser } from '../admin-users/admin-user.entity.js';
import { AdminAuditLog } from '../admin-users/admin-audit-log.entity.js';
import { ImageCompletion } from '../image-completion/image-completion.entity.js';
import { Model } from '../model/model.entity.js';
import { Upscale } from '../upscale/upscale.entity.js';
import { Board } from '../boards/boards.entity.js';
import { Organization } from '../organizations/organization.entity.js';
import { SignupCode } from '../user-signup-code/user-signup-code.entity.js';
import { GlobalNotification } from '../global-notification/global-notification.entity.js';
import { AdminUserModule } from '../admin-users/admin-user.module.js';
import { AdminSecurityService } from '../admin-users/admin-security.service.js';
import { componentLoader, Components } from '../components/components.js';
import * as crypto from 'crypto';
import axios from 'axios';

// Generate secure session secrets if not provided
const SESSION_SECRET = process.env.SESSION_SECRET || crypto.randomBytes(32).toString('hex');
const COOKIE_SECRET = process.env.COOKIE_SECRET || crypto.randomBytes(32).toString('hex');

// Debug session configuration
console.log('[ADMIN_AUTH] Session configuration:', {
    sessionSecretSet: !!process.env.SESSION_SECRET,
    cookieSecretSet: !!process.env.COOKIE_SECRET,
    nodeEnv: process.env.NODE_ENV,
    httpsEnabled: process.env.HTTPS,
});

// Dashboard handler with error handling
const dashboardHandler = async (req) => {
    try {
        const { id } = req.query;

        if (id) {
            try {
                const URL = `/internal/image_completions/${id}`;
                const imageUrl = await axios.get(process.env.API_URL + URL, {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                    timeout: 5000, // 5 second timeout
                });

                return {
                    imageUrl: imageUrl?.data?.imageUrl,
                };
            } catch (error) {
                console.error('[DASHBOARD] Error fetching single image:', error.message);
                return {
                    imageUrl: null,
                    error: 'Failed to fetch image data',
                };
            }
        }

        const page = req.query.page || 1;
        const modelpage = req.query.modelPage || 1;

        let modelList = '';
        if (req?.query?.modelList) {
            modelList =
                '&' +
                    (req?.query?.modelList
                        ?.map((id) => `modelIds[]=${id}`)
                        .join('&') ?? '');
        }
        const isHot = req.query.isHot;

        let URL =
            isHot === true || isHot === 'true'
                ? `/internal/image_completions?sortBy=createdAt&sortOrder=DESC&page=${page}&isHot=true&limit=50${modelList}`
                : `/internal/image_completions?sortBy=createdAt&sortOrder=DESC&page=${page}&limit=50${modelList}`;

        if (req.query.includeNsfw === true || req.query.includeNsfw === 'true') {
            URL += `&includeNsfw=${req.query.includeNsfw}`;
        }

        if (req.query.privacy) {
            URL += `&privacy=${req.query.privacy}`;
        }

        // Fetch data with error handling
        let images = { data: [], headers: {} };
        let models = { data: [], headers: {} };
        const errors = [];

        try {
            images = await axios.get(process.env.API_URL + URL, {
                headers: {
                    'x-api-key': process.env.API_KEY,
                },
                timeout: 5000, // 5 second timeout
            });
        } catch (error) {
            console.error('[DASHBOARD] Error fetching images:', error.message);
            errors.push('Failed to fetch image data');
        }

        try {
            models = await axios.get(
                process.env.API_URL +
                    `/internal/models?page=${modelpage}&sortBy=name&limit=35`,
                {
                    headers: {
                        'x-api-key': process.env.API_KEY,
                    },
                    timeout: 5000, // 5 second timeout
                },
            );
        } catch (error) {
            console.error('[DASHBOARD] Error fetching models:', error.message);
            errors.push('Failed to fetch model data');
        }

        return {
            images: images?.data || [],
            models: models?.data || [],
            totalModelPages: models?.headers?.['x-total-pages'] || 1,
            totalImagePages: images?.headers?.['x-total-pages'] || 1,
            errors: errors.length > 0 ? errors : undefined,
            apiStatus: errors.length === 0 ? 'connected' : 'disconnected',
        };
    } catch (error) {
        console.error('[DASHBOARD] Unexpected dashboard error:', error.message);
        return {
            images: [],
            models: [],
            totalModelPages: 1,
            totalImagePages: 1,
            errors: ['Dashboard temporarily unavailable'],
            apiStatus: 'error',
        };
    }
};

// Register AdminJS adapter
AdminJS.registerAdapter({
    Resource: AdminJSTypeorm.Resource,
    Database: AdminJSTypeorm.Database,
});

@Module({
    imports: [
        AdminUserModule,
        AdminModule.createAdminAsync({
            imports: [AdminUserModule],
            inject: [AdminSecurityService],
            useFactory: (adminSecurityService: AdminSecurityService) => ({
                adminJsOptions: {
                    rootPath: '/admin',
                    componentLoader,
                    resources: [
                        {
                            resource: User,
                            options: {
                                listProperties: [
                                    'id',
                                    'username',
                                    'email',
                                    'isActive',
                                    'blockedAt',
                                    'createdAt',
                                    'deletedAt',
                                ],
                                actions: {
                                    delete: {
                                        isVisible: false,
                                    },
                                    show: {
                                        component: Components.UserDetail,
                                        props: (record) => ({
                                            id: record.params.id,
                                        }),
                                    },
                                    edit: {
                                        component: Components.UserDetailEdit,
                                        props: (record) => ({
                                            id: record.params.id,
                                        }),
                                    },
                                },
                                sort: {
                                    sortBy: 'createdAt',
                                    direction: 'asc',
                                },
                            },
                        },
                        {
                            resource: AdminUser,
                            options: {
                                listProperties: ['name', 'email', 'role', 'status', 'lastLoginAt', 'createdAt'],
                                showProperties: ['id', 'name', 'email', 'role', 'status', 'lastLoginAt', 'allowedIpAddresses', 'permissions', 'twoFactorEnabled', 'createdAt'],
                                editProperties: ['name', 'email', 'role', 'status', 'allowedIpAddresses', 'permissions'],
                                newProperties: ['name', 'email', 'password', 'role', 'status', 'allowedIpAddresses', 'permissions'],
                                filterProperties: ['name', 'email', 'role', 'status'],
                                properties: {
                                    password: {
                                        type: 'password',
                                        isVisible: {
                                            list: false,
                                            filter: false,
                                            show: false,
                                            edit: false,
                                            new: true,
                                        },
                                        isRequired: true,
                                        props: {
                                            placeholder: 'Enter password (min 12 characters)',
                                        },
                                    },
                                    passwordHash: {
                                        isVisible: false,
                                    },
                                    twoFactorSecret: {
                                        isVisible: false,
                                    },
                                    failedLoginAttempts: {
                                        isVisible: {
                                            list: false,
                                            filter: false,
                                            show: true,
                                            edit: false,
                                            new: false,
                                        },
                                    },
                                    lockedUntil: {
                                        isVisible: {
                                            list: false,
                                            filter: false,
                                            show: true,
                                            edit: false,
                                            new: false,
                                        },
                                    },
                                    mustChangePassword: {
                                        isVisible: {
                                            list: false,
                                            filter: false,
                                            show: true,
                                            edit: false,
                                            new: false,
                                        },
                                    },
                                },
                                actions: {
                                    delete: { isVisible: false }, // Use soft delete through service
                                    edit: {
                                        before: async (request: any) => {
                                            // Validate and clean request payload
                                            if (request.payload) {
                                                // Remove sensitive fields that shouldn't be edited
                                                delete request.payload.passwordHash;
                                                delete request.payload.twoFactorSecret;
                                                delete request.payload.password;
                                                delete request.payload.failedLoginAttempts;
                                                delete request.payload.lockedUntil;
                                                delete request.payload.lastLoginAt;
                                                delete request.payload.lastLoginIp;
                                                delete request.payload.passwordChangedAt;
                                                
                                                // Validate email format
                                                if (request.payload.email) {
                                                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                                                    if (!emailRegex.test(request.payload.email)) {
                                                        throw new Error('Invalid email format');
                                                    }
                                                    request.payload.email = request.payload.email.toLowerCase();
                                                }

                                                // Validate IP addresses format if provided
                                                if (request.payload.allowedIpAddresses) {
                                                    const ipRegex = /^((25[0-5]|(2[0-4]|1\d|[1-9]|)\d)\.?\b){4}$/;
                                                    const ips = Array.isArray(request.payload.allowedIpAddresses) 
                                                        ? request.payload.allowedIpAddresses 
                                                        : [request.payload.allowedIpAddresses];
                                                    
                                                    for (const ip of ips) {
                                                        if (ip && !ipRegex.test(ip)) {
                                                            throw new Error(`Invalid IP address format: ${ip}`);
                                                        }
                                                    }
                                                }
                                            }
                                            return request;
                                        },
                                    },
                                    new: {
                                        before: async (request: any) => {
                                            // Handle password hashing and validation for new users
                                            if (request.payload) {
                                                // Validate required fields
                                                if (!request.payload.name || !request.payload.email || !request.payload.password) {
                                                    throw new Error('Name, email, and password are required');
                                                }

                                                // Validate email format
                                                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                                                if (!emailRegex.test(request.payload.email)) {
                                                    throw new Error('Invalid email format');
                                                }
                                                request.payload.email = request.payload.email.toLowerCase();

                                                // Validate password strength
                                                const passwordValidation = adminSecurityService.validatePasswordStrength(request.payload.password);
                                                if (!passwordValidation.isValid) {
                                                    throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
                                                }

                                                // Hash password
                                                request.payload.passwordHash = await adminSecurityService.hashPassword(request.payload.password);
                                                request.payload.passwordChangedAt = new Date();
                                                request.payload.mustChangePassword = false;
                                                delete request.payload.password;
                                                
                                                // Clean up other sensitive fields
                                                delete request.payload.twoFactorSecret;
                                                delete request.payload.failedLoginAttempts;
                                                delete request.payload.lockedUntil;
                                                delete request.payload.lastLoginAt;
                                                delete request.payload.lastLoginIp;
                                            }
                                            return request;
                                        },
                                    },
                                    // Add custom actions for password management
                                    changePassword: {
                                        actionType: 'record',
                                        variant: 'primary',
                                        icon: 'Key',
                                        label: 'Change Password',
                                        isVisible: true,
                                        showInDrawer: false,
                                        handler: async (request: any, response: any, context: any) => {
                                            try {
                                                const { record, currentAdmin } = context;
                                                
                                                // Handle POST request with new password  
                                                if (request.method === 'post' && request.payload) {
                                                    const { newPassword, confirmPassword } = request.payload.data || request.payload;
                                                    
                                                    if (!newPassword || !confirmPassword) {
                                                        return {
                                                            record: record.toJSON(currentAdmin),
                                                            notice: {
                                                                message: 'Both password fields are required',
                                                                type: 'error'
                                                            }
                                                        };
                                                    }
                                                    
                                                    // Validate password confirmation
                                                    if (newPassword !== confirmPassword) {
                                                        return {
                                                            record: record.toJSON(currentAdmin),
                                                            notice: {
                                                                message: 'Passwords do not match',
                                                                type: 'error'
                                                            }
                                                        };
                                                    }

                                                    // Validate password strength
                                                    const passwordValidation = adminSecurityService.validatePasswordStrength(newPassword);
                                                    if (!passwordValidation.isValid) {
                                                        return {
                                                            record: record.toJSON(currentAdmin),
                                                            notice: {
                                                                message: `Password validation failed: ${passwordValidation.errors.join(', ')}`,
                                                                type: 'error'
                                                            }
                                                        };
                                                    }

                                                    // Hash new password
                                                    const passwordHash = await adminSecurityService.hashPassword(newPassword);
                                                    
                                                    // Update user with new password
                                                    await context.resource.update(request.params.recordId, {
                                                        passwordHash,
                                                        passwordChangedAt: new Date(),
                                                        mustChangePassword: false,
                                                        failedLoginAttempts: 0,
                                                        lockedUntil: null,
                                                    });
                                                    
                                                    return {
                                                        record: record.toJSON(currentAdmin),
                                                        redirectUrl: context.h.resourceUrl({ resourceId: context.resource.id() }),
                                                        notice: {
                                                            message: 'Password changed successfully',
                                                            type: 'success'
                                                        }
                                                    };
                                                }

                                                // For GET request, just return record - component will handle the form
                                                return {
                                                    record: record.toJSON(currentAdmin),
                                                };
                                            } catch (error) {
                                                console.error('Change password error:', error);
                                                return {
                                                    record: context.record.toJSON(context.currentAdmin),
                                                    notice: {
                                                        message: `Error: ${error.message}`,
                                                        type: 'error'
                                                    }
                                                };
                                            }
                                        },
                                        component: Components.ChangePasswordForm,
                                    },
                                    resetPassword: {
                                        actionType: 'record',
                                        variant: 'danger',
                                        icon: 'Refresh',
                                        label: 'Reset Password',
                                        isVisible: true,
                                        showInDrawer: false,
                                        guard: 'Are you sure you want to reset this user\'s password? This will generate a temporary password.',
                                        handler: async (request: any, response: any, context: any) => {
                                            try {
                                                const { record, currentAdmin } = context;
                                                
                                                // Generate a secure temporary password
                                                const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
                                                let tempPassword = '';
                                                
                                                // Ensure password meets complexity requirements
                                                tempPassword += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // uppercase
                                                tempPassword += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // lowercase
                                                tempPassword += '0123456789'[Math.floor(Math.random() * 10)]; // number
                                                tempPassword += '!@#$%^&*'[Math.floor(Math.random() * 8)]; // special
                                                
                                                // Fill the rest randomly to make 12 characters
                                                for (let i = 4; i < 12; i++) {
                                                    tempPassword += charset[Math.floor(Math.random() * charset.length)];
                                                }
                                                
                                                // Shuffle the password
                                                tempPassword = tempPassword.split('').sort(() => Math.random() - 0.5).join('');
                                                
                                                const passwordHash = await adminSecurityService.hashPassword(tempPassword);
                                                
                                                // Update user with new password
                                                await context.resource.update(request.params.recordId, {
                                                    passwordHash,
                                                    passwordChangedAt: new Date(),
                                                    mustChangePassword: true,
                                                    failedLoginAttempts: 0,
                                                    lockedUntil: null,
                                                });
                                                
                                                return {
                                                    record: record.toJSON(currentAdmin),
                                                    redirectUrl: context.h.resourceUrl({ resourceId: context.resource.id() }),
                                                    notice: {
                                                        message: `Password reset successfully. Temporary password: ${tempPassword} (User must change on first login)`,
                                                        type: 'success'
                                                    }
                                                };
                                            } catch (error) {
                                                console.error('Reset password error:', error);
                                                return {
                                                    record: context.record.toJSON(context.currentAdmin),
                                                    notice: {
                                                        message: `Error resetting password: ${error.message}`,
                                                        type: 'error'
                                                    }
                                                };
                                            }
                                        },
                                    },
                                },
                            },
                        },
                        {
                            resource: ImageCompletion,
                            options: {
                                listProperties: [
                                    'id',
                                    'prompt',
                                    'promptSystem',
                                    'privacy',
                                    'isHot',
                                    'isNsfw',
                                    'generationData',
                                    'blockedAt',
                                    'createdAt',
                                    'deletedAt',
                                ],
                                actions: {
                                    delete: {
                                        isVisible: false,
                                    },
                                    show: {
                                        component: Components.ImageDetail,
                                        props: (record) => ({
                                            id: record.params.id,
                                        }),
                                    },
                                },
                                sort: {
                                    sortBy: 'createdAt',
                                    direction: 'asc',
                                },
                            },
                        },
                        {
                            resource: Model,
                            options: {
                                listProperties: [
                                    'id',
                                    'user.id',
                                    'name',
                                    'type',
                                    'class',
                                    'status',
                                    'statusDetail',
                                    'prompt',
                                    'privacy',
                                    'version',
                                    'createdAt',
                                ],
                                properties: {
                                    'user.id': {
                                        label: 'User',
                                    },
                                },
                                actions: {
                                    delete: {
                                        isVisible: false,
                                    },
                                    show: {
                                        component: Components.ModelDetail,
                                        props: (record) => ({
                                            id: record.params.id,
                                        }),
                                    },
                                },
                                sort: {
                                    sortBy: 'createdAt',
                                    direction: 'asc',
                                },
                            },
                        },


                        {
                            resource: Upscale,
                            options: {
                                listProperties: [
                                    'id',
                                    'user.id',
                                    'imageCompletion.id',
                                    'imageUrl',
                                    'status',
                                    'createdAt',
                                    'updatedAt',
                                ],
                                properties: {
                                    'user.id': {
                                        label: 'User',
                                    },
                                    'imageCompletion.id': {
                                        label: 'imageCompletion',
                                    },
                                },
                                sort: {
                                    sortBy: 'createdAt',
                                    direction: 'asc',
                                },
                            },
                        },
                        {
                            resource: Board,
                            options: {
                                listProperties: [
                                    'id',
                                    'name',
                                    'owner.id',
                                    'visibility',
                                    'safety',
                                    'members',
                                    'createdAt',
                                    'updatedAt',
                                ],
                                properties: {
                                    'owner.id': {
                                        label: 'Owner',
                                    },
                                },
                                sort: {
                                    sortBy: 'createdAt',
                                    direction: 'asc',
                                },
                            },
                        },
                        {
                            resource: Organization,
                            options: {
                                listProperties: [
                                    'id',
                                    'name',
                                    'handle',
                                    'members',
                                    'seatsPurchased',
                                    'createdAt',
                                ],
                                actions: {
                                    delete: {
                                        isVisible: false,
                                    },
                                    show: {
                                        component: Components.OrganizationDetail,
                                        props: (record) => ({
                                            id: record.params.id,
                                        }),
                                    },
                                },
                                sort: {
                                    sortBy: 'createdAt',
                                },
                            },
                        },
                        {
                            resource: SignupCode,
                            options: {
                                listPropierties: [
                                    'id',
                                    'code',
                                    'user.id',
                                    'usages',
                                    'isActive',
                                    'createdAt',
                                    'updatedAt',
                                ],
                                properties: {
                                    'user.id': {
                                        label: 'User',
                                    },
                                },
                                actions: {
                                    delete: {
                                        isVisible: false,
                                    },
                                },
                                sort: {
                                    sortBy: 'createdAt',
                                    direction: 'asc',
                                },
                            },
                        },
                        {
                            resource: GlobalNotification,
                            options: {
                                listProperties: [
                                    'title',
                                    'message',
                                    'textColor',
                                    'backgroundColor',
                                    'isActive',
                                ],
                                actions: {
                                    new: {
                                        isVisible: false,
                                    },
                                    filter: {
                                        isVisible: false,
                                    },
                                    show: {
                                        isVisible: false,
                                    },
                                    list: {
                                        showFilter: false,
                                        showResourceActions: false,
                                    },
                                    bulkDelete: {
                                        isVisible: false,
                                    },
                                    delete: {
                                        isVisible: false,
                                    },
                                    search: {
                                        isVisible: false,
                                    },
                                },
                            },
                        },
                        {
                            resource: AdminAuditLog,
                            options: {
                                listProperties: ['id', 'adminUser.name', 'action', 'resource', 'isSuccessful', 'createdAt'],
                                showProperties: ['id', 'adminUser.name', 'adminUser.email', 'action', 'resource', 'resourceId', 'oldValues', 'newValues', 'ipAddress', 'userAgent', 'description', 'isSuccessful', 'errorMessage', 'createdAt'],
                                filterProperties: ['adminUser.name', 'action', 'resource', 'isSuccessful', 'createdAt'],
                                properties: {
                                    'adminUser.name': {
                                        label: 'Admin User',
                                    },
                                    'adminUser.email': {
                                        label: 'Admin Email',
                                    },
                                    action: {
                                        label: 'Action',
                                    },
                                    resource: {
                                        label: 'Resource',
                                    },
                                    isSuccessful: {
                                        label: 'Success',
                                        type: 'boolean',
                                    },
                                },
                                actions: {
                                    new: { isVisible: false },
                                    edit: { isVisible: false },
                                    delete: { isVisible: false },
                                },
                                sort: {
                                    sortBy: 'createdAt',
                                    direction: 'desc',
                                },
                            },
                        },
                    ],
                    dashboard: {
                        handler: dashboardHandler,
                        component: Components.ImageList,
                    },
                    pages: {
                        statistics: {
                            label: 'Statistics',
                            component: Components.Statistics,
                        },
                    },
                    env: {
                        API_URL: process.env.API_URL,
                        API_KEY: process.env.API_KEY,
                    },
                },
                auth: {
                    authenticate: async (email: string, password: string, context: any) => {
                        try {
                            console.log(`[ADMIN_AUTH] Authentication attempt for: ${email}`);
                            console.log(`[ADMIN_AUTH] Context available:`, {
                                hasContext: !!context,
                                hasRequest: !!context?.request,
                                hasSession: !!context?.request?.session,
                                sessionKeys: context?.request?.session ? Object.keys(context.request.session) : 'no session',
                                sessionId: context?.request?.sessionID || 'no sessionID',
                                cookies: context?.request?.headers?.cookie ? 'cookies present' : 'no cookies',
                            });

                            const ipAddress = context?.request?.ip || context?.request?.connection?.remoteAddress || 'unknown';
                            const userAgent = context?.request?.headers?.['user-agent'] || 'unknown';

                            const authResult = await adminSecurityService.authenticate({
                                email,
                                password,
                                ipAddress,
                                userAgent,
                            });

                            if (authResult) {
                                console.log(`[ADMIN_AUTH] Successful login for: ${email} (${authResult.user.role})`);

                                // Initialize session if it doesn't exist
                                if (!context?.request?.session) {
                                    console.error(`[ADMIN_AUTH] CRITICAL: No session object available for: ${email}`);
                                    console.error(`[ADMIN_AUTH] Request object:`, {
                                        hasRequest: !!context?.request,
                                        requestKeys: context?.request ? Object.keys(context.request) : 'no request',
                                        sessionMiddleware: 'may not be configured properly',
                                    });
                                } else {
                                    // Store session token in AdminJS session
                                    context.request.session.sessionToken = authResult.sessionToken;
                                    context.request.session.adminUserId = authResult.user.id;
                                    context.request.session.userEmail = authResult.user.email;
                                    context.request.session.userRole = authResult.user.role;

                                    // Force session save
                                    if (context.request.session.save) {
                                        context.request.session.save((err) => {
                                            if (err) {
                                                console.error(`[ADMIN_AUTH] Session save error:`, err);
                                            } else {
                                                console.log(`[ADMIN_AUTH] Session saved successfully for: ${email}`);
                                            }
                                        });
                                    }

                                    console.log(`[ADMIN_AUTH] Session token stored in AdminJS session for: ${email}`, {
                                        sessionId: context.request.sessionID,
                                        tokenLength: authResult.sessionToken.length,
                                        userId: authResult.user.id,
                                    });
                                }

                                return {
                                    id: authResult.user.id,
                                    email: authResult.user.email,
                                    name: authResult.user.name,
                                    role: authResult.user.role,
                                    sessionToken: authResult.sessionToken,
                                };
                            }

                            console.log(`[ADMIN_AUTH] Failed login for: ${email}`);
                            return null;
                        } catch (error) {
                            console.error(`[ADMIN_AUTH] Authentication error for ${email}:`, error.message);
                            return null;
                        }
                    },
                    cookieName: 'adminjs_session',
                    cookiePassword: COOKIE_SECRET,
                    // Add session validation for subsequent requests
                    isAuthenticated: async (context: any) => {
                        try {
                            console.log('[ADMIN_AUTH] Session validation attempt', {
                                hasContext: !!context,
                                hasRequest: !!context?.request,
                                hasSession: !!context?.request?.session,
                                sessionId: context?.request?.sessionID || 'no sessionID',
                                cookies: context?.request?.headers?.cookie ? 'cookies present' : 'no cookies',
                            });

                            const session = context?.request?.session;
                            if (!session) {
                                console.log('[ADMIN_AUTH] No session object in request');
                                return false;
                            }

                            if (!session.sessionToken || !session.adminUserId) {
                                console.log('[ADMIN_AUTH] No session token or user ID in session', {
                                    hasToken: !!session.sessionToken,
                                    hasUserId: !!session.adminUserId,
                                    sessionKeys: Object.keys(session),
                                });
                                return false;
                            }

                            const ipAddress = context?.request?.ip || context?.request?.connection?.remoteAddress || 'unknown';
                            console.log(`[ADMIN_AUTH] Validating session token: ${session.sessionToken.substring(0, 8)}... for user ID: ${session.adminUserId}`);

                            const user = await adminSecurityService.validateSession(session.sessionToken, ipAddress);

                            if (!user) {
                                console.log(`[ADMIN_AUTH] Session validation failed for token: ${session.sessionToken.substring(0, 8)}...`);
                                return false;
                            }

                            console.log(`[ADMIN_AUTH] Session validated successfully for user: ${user.email}`);
                            return {
                                id: user.id,
                                email: user.email,
                                name: user.name,
                                role: user.role,
                                sessionToken: session.sessionToken,
                            };
                        } catch (error) {
                            console.error('[ADMIN_AUTH] Session validation error:', error.message);
                            return false;
                        }
                    },
                    // Add logout handler
                    logout: async (context: any) => {
                        try {
                            const session = context?.request?.session;
                            if (session && session.sessionToken) {
                                const ipAddress = context?.request?.ip || context?.request?.connection?.remoteAddress || 'unknown';
                                const userAgent = context?.request?.headers?.['user-agent'] || 'unknown';

                                await adminSecurityService.logout(session.sessionToken, ipAddress, userAgent);
                                console.log(`[ADMIN_AUTH] User logged out, session token: ${session.sessionToken.substring(0, 8)}...`);

                                // Clear session data
                                delete session.sessionToken;
                                delete session.adminUserId;
                            }
                        } catch (error) {
                            console.error('[ADMIN_AUTH] Logout error:', error.message);
                        }
                    },
                },
                sessionOptions: {
                    resave: false,
                    saveUninitialized: true, // Changed to true to ensure session is created
                    secret: SESSION_SECRET,
                    cookie: {
                        secure: false, // Disabled for debugging
                        httpOnly: true,
                        maxAge: 8 * 60 * 60 * 1000, // 8 hours
                        sameSite: 'lax',
                    },
                    name: 'adminjs_session',
                    rolling: true,
                },
            }),
        }),
    ],
})
export class AdminJSModule {}
