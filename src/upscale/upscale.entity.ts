import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    ManyToOne,
    BaseEntity
  } from 'typeorm';
  import { User } from '../users/user.entity.js';
  import { ImageCompletion } from '../image-completion/image-completion.entity.js';
  
  export enum StatusEnum {
    NEW = 'new',
    GENERATING = 'generating',
    READY = 'ready',
    FAILED = 'failed',
  }
  
  @Entity('upscale')
  export class Upscale extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => User, { eager: true })
    user: User;

    @Column({ nullable: true })
    userId?: string;

    @ManyToOne(() => ImageCompletion, { lazy: true })
    imageCompletion: ImageCompletion;
  
    @Column({ nullable: true })
    imageCompletionId: string;
  
    @Column({ type: 'text', nullable: true })
    imageUrl?: string;
  
    @Column({ type: 'text', nullable: true })
    prompt?: string;
  
    @Column({ type: 'text', nullable: true })
    promptSystem?: string;
  
    @Column({ nullable: false, default: 'new' })
    status: string;
  
    @Column({ type: 'text', nullable: true })
    statusDetail?: string;
  
    @Column({ nullable: false, default: 0 })
    progress: number;
  
    @Column({ type: 'text', nullable: true })
    storageBucket?: string;
  
    @Column({ type: 'text', nullable: true })
    storagePath?: string;
  
    @Column({ type: 'text', nullable: true })
    imagePath?: string;
  
    @Column({ type: 'json', nullable: true, default: {} })
    generationSettings?: any;
  
    @Column({ type: 'text', nullable: true })
    generationData?: string;
  
    @Column({ nullable: true })
    generatedByUnit: string;
  
    @Column({ nullable: false, default: 0 })
    generationSeconds: number;
  
    @Column({ default: false })
    isSelected: boolean;
  
    @Column({ default: false })
    isActive: boolean;
  
    @Column({ type: 'timestamp', nullable: true })
    blockedAt?: Date;
  
    @DeleteDateColumn({ type: 'timestamp', nullable: true })
    deletedAt?: Date;
  
    @CreateDateColumn()
    createdAt: Date;
  
    @UpdateDateColumn()
    updatedAt: Date;
  }