import * as crypto from 'crypto';

// Basic security enhancements that work with the current setup
export class BasicSecurity {
    private static readonly MAX_LOGIN_ATTEMPTS = 5;
    private static readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes
    private static loginAttempts = new Map<string, { count: number; lastAttempt: number; lockedUntil?: number }>();

    static generateSecureSecret(): string {
        return crypto.randomBytes(32).toString('hex');
    }

    static validatePasswordStrength(password: string): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];
        const minLength = 12;

        if (password.length < minLength) {
            errors.push(`Password must be at least ${minLength} characters long`);
        }

        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }

        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }

        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }

        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }

        return {
            isValid: errors.length === 0,
            errors,
        };
    }

    static checkRateLimit(identifier: string): { allowed: boolean; message?: string } {
        const now = Date.now();
        const attempt = this.loginAttempts.get(identifier);

        if (!attempt) {
            this.loginAttempts.set(identifier, { count: 1, lastAttempt: now });
            return { allowed: true };
        }

        // Check if still locked
        if (attempt.lockedUntil && now < attempt.lockedUntil) {
            const remainingTime = Math.ceil((attempt.lockedUntil - now) / 1000 / 60);
            return { 
                allowed: false, 
                message: `Account locked. Try again in ${remainingTime} minutes.` 
            };
        }

        // Reset if lockout period has passed
        if (attempt.lockedUntil && now >= attempt.lockedUntil) {
            this.loginAttempts.set(identifier, { count: 1, lastAttempt: now });
            return { allowed: true };
        }

        // Increment attempt count
        attempt.count++;
        attempt.lastAttempt = now;

        // Lock if too many attempts
        if (attempt.count >= this.MAX_LOGIN_ATTEMPTS) {
            attempt.lockedUntil = now + this.LOCKOUT_DURATION;
            const lockoutMinutes = this.LOCKOUT_DURATION / 1000 / 60;
            return { 
                allowed: false, 
                message: `Too many failed attempts. Account locked for ${lockoutMinutes} minutes.` 
            };
        }

        return { allowed: true };
    }

    static recordSuccessfulLogin(identifier: string): void {
        this.loginAttempts.delete(identifier);
    }

    static logSecurityEvent(event: {
        action: string;
        identifier?: string;
        ipAddress?: string;
        userAgent?: string;
        success: boolean;
        message?: string;
    }): void {
        const timestamp = new Date().toISOString();
        console.log(`[SECURITY] ${timestamp} - ${event.action}`, {
            identifier: event.identifier,
            ipAddress: event.ipAddress,
            success: event.success,
            message: event.message,
        });
    }

    static getClientIp(req: any): string {
        return req.ip || 
               req.connection?.remoteAddress || 
               req.socket?.remoteAddress || 
               req.headers['x-forwarded-for']?.split(',')[0] || 
               'unknown';
    }

    static isStrongPassword(password: string): boolean {
        return this.validatePasswordStrength(password).isValid;
    }

    static sanitizeInput(input: string): string {
        return input.replace(/[<>\"'&]/g, '');
    }
}
