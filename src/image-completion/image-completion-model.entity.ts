import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, ManyToOne, BaseEntity } from 'typeorm';
import { ImageCompletion } from './image-completion.entity.js';
import { Model } from '../model/model.entity.js';

@Entity('image_completion_model')
export class ImageCompletionModel extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ImageCompletion)
  imageCompletion: ImageCompletion;

  @ManyToOne(() => Model, { eager: true })
  model: Model;
}
