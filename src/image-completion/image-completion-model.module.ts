import { Module } from '@nestjs/common';
import { DatabaseModule } from '../database.module.js';
import { imageCompletionModelProviders } from './image-completion-model.provider.js';
import { ImageCompletionModelService } from './image-completion-model.service.js';

@Module({
    imports: [DatabaseModule],
    providers: [...imageCompletionModelProviders, ImageCompletionModelService],
})
export class ImageCompletionModelModule {}
