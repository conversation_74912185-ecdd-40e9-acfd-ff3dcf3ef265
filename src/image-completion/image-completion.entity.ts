import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { User } from '../users/user.entity.js';

export enum PrivacyEnum {
    PUBLIC = 'public',
    PRIVATE = 'private',
    LICENSED = 'licensed',
}

export enum StatusEnum {
    NEW = 'new',
    GENERATING = 'generating',
    READY = 'ready',
    FAILED = 'failed',
}

@Entity('image_completion')
export class ImageCompletion extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => User)
    user: User;

    // @OneToMany(
    //     () => ImageCompletionModel,
    //     (imageCompletionModel) => imageCompletionModel.imageCompletion,
    //     { cascade: true, eager: true },
    // )
    // models: ImageCompletionModel[];

    @Column({ nullable: true })
    userId?: string;

    @Column({ type: 'text', nullable: true })
    prompt?: string;

    @Column({ type: 'text', nullable: true })
    promptSystem?: string;

    @Column({ type: 'text', nullable: true })
    baseModel?: string;

    @Column({ nullable: false, default: 'new' })
    status: string;

    @Column({ nullable: false, default: 0 })
    progress: number;

    @Column({ type: 'text', nullable: true })
    previewImage?: string;

    @Column({ nullable: false })
    privacy: string;

    @Column({ nullable: false, default: 0 })
    likes: number;

    @Column({ nullable: false, default: 0 })
    reports: number;

    @Column({ type: 'text', nullable: true })
    storageBucket?: string;

    @Column({ type: 'text', nullable: true })
    storagePath?: string;

    @Column({ type: 'json', nullable: true })
    imagePaths?: any;

    @Column({ type: 'text', nullable: true })
    generationData?: string;

    @Column({ default: true })
    hasWatermark: boolean;

    @Column({ default: true })
    isNsfw: boolean;

    @Column({ default: false })
    isHot: boolean;

    @Column({ default: false })
    isActive: boolean;

    @Column({ type: 'timestamp', nullable: true })
    blockedAt?: Date;

    @DeleteDateColumn({ type: 'timestamp', nullable: true })
    deletedAt?: Date;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    // hasModel(modelId: string): boolean {
    //     return this.models.some(
    //         (imageCompletionModel) => imageCompletionModel.model.id === modelId,
    //     );
    // }
}
