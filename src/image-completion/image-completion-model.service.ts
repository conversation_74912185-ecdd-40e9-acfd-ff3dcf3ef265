import { Inject, Injectable } from "@nestjs/common";
import { Repository } from "typeorm";
import { ImageCompletionModel } from "./image-completion-model.entity.js";

@Injectable()
export class ImageCompletionModelService {
    constructor(
        @Inject("IMAGE_COMPLETION_MODEL_REPOSITORY")
        private imageCompletionModelRepository: Repository<ImageCompletionModel>
    ){}

    async findAll(): Promise<ImageCompletionModel[]> {
        return this.imageCompletionModelRepository.find();
    }
}