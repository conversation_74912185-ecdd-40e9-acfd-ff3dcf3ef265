import { Inject, Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { ImageCompletion } from './image-completion.entity.js';

@Injectable()
export class ImageCompletionService {
    constructor(
        @Inject('IMAGE_COMPLETION_REPOSITORY')
        private imageCompletionRepository: Repository<ImageCompletion>,
    ) {}

    async findAll(): Promise<ImageCompletion[]> {
        return this.imageCompletionRepository.find();
    }
}
