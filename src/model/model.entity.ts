import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { User } from '../users/user.entity.js';

export enum PrivacyEnum {
    PUBLIC = 'public',
    PRIVATE = 'private',
    LICENSED = 'licensed',
}

export enum StatusEnum {
    NEW = 'new',
    PENDING = 'pending',
    TRAINING = 'training',
    FINISHED = 'finished',
    AVAILABLE = 'available',
    FAILED = 'failed',
}

export enum ClassEnum {
    OBJECT = 'object',
    PERSON = 'person',
    STYLE = 'style',
}

@Entity('model')
export class Model extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => User, { eager: true })
    user: User;

    @Column({ nullable: true })
    userId?: string;

    @Column({ nullable: false })
    name: string;

    @Column({ nullable: true })
    type?: string;

    @Column({
        type: 'enum',
        enum: ClassEnum,
        default: null,
        nullable: true,
    })
    class?: ClassEnum;

    @Column({
        type: 'enum',
        enum: StatusEnum,
        default: StatusEnum.NEW,
        nullable: false,
    })
    status: StatusEnum;

    @Column({ type: 'text', nullable: true })
    statusDetail?: string;

    @Column({ nullable: false, default: 0 })
    progress: number;

    @Column({ type: 'text', nullable: true })
    prompt?: string;

    @Column({
        type: 'enum',
        enum: PrivacyEnum,
        default: PrivacyEnum.PUBLIC,
        nullable: false,
    })
    privacy: PrivacyEnum;

    @Column({ nullable: false, default: 0 })
    usages: number;

    @Column({ nullable: false, default: 0 })
    likes: number;

    @Column({ nullable: false, default: 0 })
    reports: number;

    @Column({ nullable: false, default: 1 })
    version: number;

    @Column({ type: 'text', nullable: true })
    storageBucket?: string;

    @Column({ type: 'text', nullable: true })
    storagePath?: string;

    @Column({ type: 'text', nullable: true })
    thumbnail?: string;

    @Column({ type: 'json', nullable: true })
    thumbnailOptions?: any;

    @Column({ default: false })
    isActive: boolean;

    @Column({ type: 'timestamp', nullable: true })
    blockedAt?: Date;

    @DeleteDateColumn({ type: 'timestamp', nullable: true })
    deletedAt?: Date;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
