#!/usr/bin/env node

import { HttpAuthService } from '../auth/http-auth.service.js';
import * as fs from 'fs';
import * as path from 'path';

const httpAuthService = new HttpAuthService();

function showUsage() {
    console.log(`
🔐 HTTP Basic Auth Setup Tool

Usage:
  npm run http-auth:generate     - Generate secure HTTP auth credentials
  npm run http-auth:config       - Show current HTTP auth configuration
  npm run http-auth:test         - Test HTTP auth credentials
  npm run http-auth:disable      - Disable HTTP auth
  npm run http-auth:enable       - Enable HTTP auth

Environment Variables:
  HTTP_AUTH_USERNAME=admin       - HTTP Basic Auth username
  HTTP_AUTH_PASSWORD=secure123   - HTTP Basic Auth password
  HTTP_AUTH_REALM=Letz.ai Admin  - Authentication realm name
  HTTP_AUTH_ENABLED=true         - Enable/disable HTTP auth
  SKIP_HTTP_AUTH_DEV=false       - Skip HTTP auth in development
`);
}

function generateCredentials() {
    console.log('🔐 Generating secure HTTP Basic Auth credentials...\n');
    
    const credentials = httpAuthService.generateSecureCredentials();
    const envContent = `
# HTTP Basic Authentication Configuration
HTTP_AUTH_USERNAME=${credentials.username}
HTTP_AUTH_PASSWORD=${credentials.password}
HTTP_AUTH_REALM=Letz.ai Admin Panel
HTTP_AUTH_ENABLED=true
SKIP_HTTP_AUTH_DEV=false
`;

    console.log('✅ Generated credentials:');
    console.log(`   Username: ${credentials.username}`);
    console.log(`   Password: ${credentials.password}`);
    console.log('\n📝 Add these to your .env file:');
    console.log(envContent);
    
    // Optionally append to .env file
    const envPath = path.join(process.cwd(), '.env');
    if (fs.existsSync(envPath)) {
        console.log('\n❓ Would you like to append these to your .env file? (y/N)');
        // In a real implementation, you'd use readline for user input
        console.log('   (Manual addition recommended for security)');
    }
    
    console.log('\n🔒 Security Notes:');
    console.log('   • Store credentials securely');
    console.log('   • Use different credentials for production');
    console.log('   • Consider using environment-specific configs');
}

function showConfig() {
    console.log('🔐 Current HTTP Basic Auth Configuration:\n');
    
    const config = httpAuthService.getConfig();
    
    console.log(`   Enabled: ${config.enabled ? '✅ Yes' : '❌ No'}`);
    console.log(`   Username: ${config.username}`);
    console.log(`   Realm: ${config.realm}`);
    console.log(`   Skip in Dev: ${config.skipInDev ? '✅ Yes' : '❌ No'}`);
    console.log(`   Password Set: ${process.env.HTTP_AUTH_PASSWORD ? '✅ Yes' : '❌ No'}`);
    
    if (!config.enabled) {
        console.log('\n⚠️  HTTP Basic Auth is currently DISABLED');
        console.log('   Set HTTP_AUTH_ENABLED=true to enable');
    }
    
    if (config.skipInDev && process.env.NODE_ENV === 'development') {
        console.log('\n⚠️  HTTP Basic Auth is SKIPPED in development mode');
        console.log('   Set SKIP_HTTP_AUTH_DEV=false to enforce in development');
    }
}

function testCredentials() {
    console.log('🔐 Testing HTTP Basic Auth credentials...\n');
    
    const username = process.env.HTTP_AUTH_USERNAME || 'admin';
    const password = process.env.HTTP_AUTH_PASSWORD || 'secure123';
    
    console.log(`Testing with username: ${username}`);
    
    const isValid = httpAuthService.validateCredentials(username, password);
    
    if (isValid) {
        console.log('✅ Credentials are valid!');
        
        const authHeader = httpAuthService.getAuthHeader();
        console.log(`\n📋 Authorization Header:`);
        console.log(`   Authorization: ${authHeader}`);
        
        console.log(`\n🌐 Test with curl:`);
        console.log(`   curl -H "Authorization: ${authHeader}" http://localhost:3013/admin`);
    } else {
        console.log('❌ Credentials are invalid!');
        console.log('   Check your HTTP_AUTH_USERNAME and HTTP_AUTH_PASSWORD');
    }
}

function disableAuth() {
    console.log('🔐 Disabling HTTP Basic Auth...\n');
    console.log('Add this to your .env file:');
    console.log('HTTP_AUTH_ENABLED=false');
    console.log('\n⚠️  This will disable the HTTP Basic Auth layer');
    console.log('   Only AdminJS authentication will be active');
}

function enableAuth() {
    console.log('🔐 Enabling HTTP Basic Auth...\n');
    console.log('Add this to your .env file:');
    console.log('HTTP_AUTH_ENABLED=true');
    console.log('\n✅ This will enable the HTTP Basic Auth layer');
    console.log('   Two-tier authentication will be active');
}

// Main execution
const command = process.argv[2];

switch (command) {
    case 'generate':
        generateCredentials();
        break;
    case 'config':
        showConfig();
        break;
    case 'test':
        testCredentials();
        break;
    case 'disable':
        disableAuth();
        break;
    case 'enable':
        enableAuth();
        break;
    default:
        showUsage();
        break;
}
