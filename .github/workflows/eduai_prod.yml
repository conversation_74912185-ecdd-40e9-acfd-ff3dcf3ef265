on:
    push:
        tags:
            - eduai-prod
            - eduai-prod-*

name: Deploy to EduAI.lu Prod

permissions:
    id-token: write
    contents: read

jobs:
    deploy:
        name: Deploy
        runs-on: ubuntu-latest

        steps:
            - name: Checkout
              uses: actions/checkout@v2

            - name: Configure A<PERSON> credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  role-to-assume: arn:aws:iam::376129849499:role/github-actions-iam-user-role
                  role-session-name: GitHub_to_AWS_via_FederatedOIDC
                  aws-region: 'eu-north-1'

            - name: Login to Amazon ECR
              id: login-ecr
              uses: aws-actions/amazon-ecr-login@v1

            - name: Build, tag, and push image to Amazon ECR
              id: build-image
              env:
                  ECR_REPOSITORY_URI: 376129849499.dkr.ecr.eu-north-1.amazonaws.com/eduai-admin-prod
                  IMAGE_TAG: latest
              run: |
                  docker build -t $ECR_REPOSITORY_URI:$IMAGE_TAG .
                  docker push $ECR_REPOSITORY_URI:$IMAGE_TAG

            - name: Deploy new image to ECS Service
              env:
                  CLUSTER: eduai-prod-services
                  SERVICE: admin
                  REGION: eu-north-1
              run: |
                  aws ecs update-service --cluster $CLUSTER --service $SERVICE --force-new-deployment --region $REGION
