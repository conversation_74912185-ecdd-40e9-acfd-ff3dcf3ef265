# Letz.ai Admin Dashboard - WARP Guide

## Project Overview

The Letz.ai Admin Dashboard is a secure, NestJS-based administrative interface for managing the [letz.ai](https://letz.ai) platform. It provides comprehensive tools for user management, content moderation, analytics, and system administration through a modern web interface built with AdminJS.

## Quick Start

### Prerequisites
- Node.js 18+ (required for optimal AdminJS functionality)
- PostgreSQL database
- Docker (required for development environment)
- SSH access to development server

### Development Environment Setup

#### Environment Access and URLs
- **Development Environment (ACCESSIBLE)**:
  - Server: `dev.gonser.com.br`
  - Admin URL: `https://admin.letzai.gonser.com.br/`
  - **AI Agent Access**: ✅ Full access via SSH and Docker commands

- **Production Environment (NO ACCESS)**:
  - Admin URL: `https://admin.letz.ai/`
  - **AI Agent Access**: ❌ No access - deployment only via GitHub Actions

#### Local Development Setup

```bash
# 1. Edit code locally at /home/<USER>/projects/letzai/admin
# 2. Copy environment configuration (if not exists)
cp .env.dist .env

# 3. Connect to remote development server
ssh -t <EMAIL> 'cd projects/letzai/admin && exec bash'

# 4. On remote server: Install dependencies
yarn install

# 5. On remote server: Generate secure session secrets
npm run generate:secrets

# 6. On remote server: Start development with Docker
docker-compose up --build

# 7. Access dashboard
open https://admin.letzai.gonser.com.br/
```

#### Important Development Rules
- **Code Editing**: Code is edited locally at `/home/<USER>/projects/letzai/admin`
- **Project Execution**: Project runs exclusively on remote server at `dev.gonser.com.br`
- **Path Structure**: Same path structure on both local and remote (`/home/<USER>/projects/letzai/admin`)
- **Remote Access**: Use `ssh -t <EMAIL> 'cd projects/letzai/admin && exec bash'` for remote shell
- **Execution Rule**: Always run the project with Docker on the remote environment - no local execution

## Key Features

### 🔐 Security & Authentication
- **Multi-layer authentication** with HTTP Basic Auth + AdminJS auth
- **Role-based access control** (super_admin, admin, moderator, viewer)
- **Two-factor authentication** (2FA) with TOTP support
- **Account lockout protection** (5 attempts, 15-minute lockout)
- **Session management** with 8-hour duration
- **Password complexity requirements** (12+ chars, mixed case, special chars)
- **Audit logging** for all administrative actions

### 👥 User Management
- **Multi-user support** with individual admin accounts
- **Flexible role permissions** system
- **User account lockout/unlock** capabilities
- **Password change enforcement**
- **IP address restrictions** (optional)

### 📊 Data Management
- **TypeORM-based** entity management
- **Auto-generated CRUD interfaces** for database tables
- **Custom components** and pages
- **API integration** for external data sources
- **Migration support** for database schema changes

### 🛡️ Rate Limiting & Protection
- **Login rate limiting** (5 attempts per 15 minutes)
- **General API rate limiting** (100 requests per 15 minutes)
- **Security headers** via Helmet.js
- **CSRF protection** with SameSite cookies

## Architecture

### Core Components

```
src/
├── admin/              # AdminJS configuration and components
├── auth/               # Authentication middleware and services
├── components/         # Custom AdminJS components
├── migrations/         # Database migration files
├── entities/           # TypeORM entity definitions
├── modules/           # Feature modules (users, models, boards, etc.)
└── scripts/           # Utility scripts for setup and management
```

### Database Integration
- **PostgreSQL** with TypeORM ORM
- **Snake case naming strategy** for database consistency
- **SSL support** for production environments
- **Connection pooling** and optimization

### Security Architecture
- **HTTP Basic Auth** middleware for initial protection
- **AdminJS authentication** for user-specific access
- **Session-based** authentication with secure cookies
- **Bcrypt password hashing** with 12 salt rounds
- **Environment-based** configuration for different deployments

## Development Workflow

### Remote Development Process

1. **Edit Code Locally**: Make changes in `/home/<USER>/projects/letzai/admin`
2. **Connect to Remote Server**: `ssh -t <EMAIL> 'cd projects/letzai/admin && exec bash'`
3. **Run Docker Commands**: Execute all Docker commands on the remote server
4. **Test Changes**: Access `https://admin.letzai.gonser.com.br/` to test

### Docker Configuration

#### Primary Development Configuration
- **Main Config**: `docker-compose.yml` - Use this for standard development
- **Development Mode**: Configured with `start:dev` for hot reloading
- **Port Mapping**: Container port 3013 mapped to host port 3013
- **Volume Mount**: `.:/usr/src/app` for live code updates

#### Docker Commands (Execute on Remote Server)

```bash
# Primary development command
docker-compose up --build

# Alternative Docker operations
npm run docker:build     # Build Docker image
npm run docker:run       # Run containerized app
npm run docker:build:fast  # Fast build for development

# Docker management
docker-compose down      # Stop and remove containers
docker-compose logs -f   # Follow container logs
docker system prune      # Clean up Docker resources
```

### Common Commands (Execute on Remote Server)

```bash
# Development (via Docker)
docker-compose up --build # Primary development command
npm run start:dev        # Watch mode development (inside container)
npm run start:debug      # Debug mode with inspector

# Building & Production
npm run build            # Compile TypeScript
npm run start:prod       # Production server

# Testing & Quality
npm run test             # Run unit tests
npm run test:e2e         # End-to-end tests
npm run lint             # ESLint code checking
npm run format           # Prettier code formatting
```

### User Management Commands (Execute on Remote Server)

**⚠️ IMPORTANT**: All these commands must be run on the remote server at `dev.gonser.com.br`

**✨ NEW**: The CLI now connects directly to the database and applies changes immediately!

```bash
# Connect to remote server first
ssh -t <EMAIL> 'cd projects/letzai/admin && exec bash'

# Then run commands on remote server:

# Create a new admin user (directly in database)
npm run admin:create -- --name "John Doe" --email "<EMAIL>" --role admin --show-password

# Change user password (directly in database)
npm run admin:password -- --email "<EMAIL>" --show-password

# Lock a user account (directly in database)
npm run admin:lock -- --email "<EMAIL>"

# Unlock a user account (directly in database)
npm run admin:unlock -- --email "<EMAIL>"

# Delete a user account - soft delete (directly in database)
npm run admin:delete -- --email "<EMAIL>"

# Delete a user account - hard delete (IRREVERSIBLE)
npm run admin:delete -- --email "<EMAIL>" --hard

# Generate secure password
npm run generate:password -- --length 16

# Generate session secrets
npm run generate:secrets
```

#### Alternative Direct Commands

```bash
# Using the main admin CLI directly (after connecting to remote server)
npm run admin create -n "John Doe" -e "<EMAIL>" -r admin
npm run admin change-password -e "<EMAIL>" --show-password
npm run admin lock -e "<EMAIL>"
npm run admin unlock -e "<EMAIL>"
npm run admin delete -e "<EMAIL>"
npm run admin generate-password -l 20
npm run admin generate-secrets
```

#### Advanced Usage Examples

```bash
# Create user with custom password and save to file
npm run admin create -n "Jane Smith" -e "<EMAIL>" -p "MySecure123!" -f create-jane.sql

# Generate and display password when creating user
npm run admin create -n "Bob Wilson" -e "<EMAIL>" --show-password

# Hard delete user (irreversible)
npm run admin delete -e "<EMAIL>" --hard

# Generate 20-character password
npm run admin generate-password -l 20

# Get help for any command
npm run admin --help
npm run admin create --help
```

### Security Setup Scripts

```bash
# Setup security infrastructure
npm run security:setup

# HTTP authentication management
npm run http-auth:generate  # Generate HTTP auth credentials
npm run http-auth:config    # Configure HTTP auth
npm run http-auth:test      # Test HTTP auth setup
npm run http-auth:enable    # Enable HTTP auth
npm run http-auth:disable   # Disable HTTP auth
```

## Configuration

### Environment Variables

```bash
# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=letzai
POSTGRES_PASSWORD=your-secure-password
POSTGRES_DB=letzai

# Security Configuration
SESSION_SECRET=your-64-char-session-secret
COOKIE_SECRET=your-64-char-cookie-secret
NODE_ENV=development

# Optional Security Features
ENFORCE_SESSION_IP=false
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15
SESSION_DURATION_HOURS=8
ALLOWED_IPS=*************,*********

# Rate Limiting
RATE_LIMIT_WINDOW_MINUTES=15
RATE_LIMIT_MAX_REQUESTS=100
LOGIN_RATE_LIMIT_MAX_ATTEMPTS=5

# HTTP Basic Auth (optional)
HTTP_AUTH_USERNAME=admin
HTTP_AUTH_PASSWORD=secure-password
HTTP_AUTH_ENABLED=true
```

## Deployment

### Docker Deployment

```bash
# Standard build
docker-compose up --build

# Fast development build
npm run docker:build:fast
npm run docker:run:fast

# Production build
npm run docker:build:prod
```

### Production Checklist

- [ ] Set `NODE_ENV=production`
- [ ] Configure SSL certificates for database
- [ ] Generate secure session secrets
- [ ] Enable HTTP Basic Auth
- [ ] Configure IP restrictions (if needed)
- [ ] Set up monitoring and logging
- [ ] Run database migrations
- [ ] Create initial admin user
- [ ] Test authentication flows
- [ ] Configure rate limiting
- [ ] Enable security headers

## Extending the Dashboard

### Adding New Entities

1. **Import entity** from letz.ai-api project
2. **Extend BaseEntity** class
3. **Create module** files (module, provider, service)
4. **Add to AppModule** imports
5. **Configure AdminJS** resource options

### Custom Components

1. **Register component** in `src/components/components.ts`
2. **Specify injection point** in AppModule
3. **Pass data handlers** if API data is needed

### Custom Pages

```javascript
// In app.module.ts
pages: {
    customAnalytics: {
        label: 'Analytics Dashboard',
        component: Components.analyticsPage,
        handler: analyticsHandler,
    },
}
```

## Security Best Practices

### Password Security
- Minimum 12 characters with complexity requirements
- Regular password changes (90 days recommended)
- Unique passwords for each admin account
- Enable 2FA for all administrators

### Network Security
- Use HTTPS in production environments
- Configure IP whitelisting for sensitive deployments
- Use VPN for remote administrative access
- Monitor network traffic and access patterns

### Monitoring & Auditing
- Regular review of audit logs
- Set up alerts for suspicious activities
- Monitor failed login attempts
- Track privilege escalations and role changes

## Troubleshooting

### Common Issues

**Database Connection Failed**
```bash
# Check database status
pg_ctl status
# Verify credentials in .env
# Test connection manually
```

**Authentication Not Working**
```bash
# Reset HTTP auth
npm run http-auth:config
# Check session secrets
npm run generate:secrets
# Verify user exists in database
```

**Docker Build Issues**
```bash
# Clean build
docker system prune
npm run docker:build:fast
# Check Dockerfile and dependencies
```

### Performance Optimization

- Use connection pooling for database
- Enable query caching in TypeORM
- Optimize AdminJS bundle size
- Configure proper logging levels
- Monitor memory usage in production

## API Integration

The dashboard integrates with internal APIs using designated API keys. Configure API connections in the respective service modules and ensure proper authentication headers are included.

## Contributing

1. Follow existing code patterns and TypeScript best practices
2. Maintain security standards for all new features
3. Update documentation for new functionality
4. Test authentication and authorization flows
5. Ensure proper error handling and logging

## Support & Documentation

- **README.md** - Basic setup and running instructions
- **SECURITY.md** - Comprehensive security guide and features
- **USER_MANAGEMENT.md** - User creation and management guide
- **SECURITY_QUICKSTART.md** - Quick security setup guide
- **Code Comments** - Inline documentation throughout the codebase

## Version Information

- **Framework**: NestJS with AdminJS
- **Database**: PostgreSQL with TypeORM
- **Authentication**: Multi-layer with 2FA support
- **Security**: Enhanced with audit logging and rate limiting
- **Deployment**: Docker-ready with multiple build configurations

---

**Note**: This is a security-critical administrative system. Always follow security best practices, regularly update dependencies, and monitor for security vulnerabilities. Never expose administrative interfaces to public networks without proper security measures.
